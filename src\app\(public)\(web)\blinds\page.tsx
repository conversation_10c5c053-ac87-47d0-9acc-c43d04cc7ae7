"use client";

import React from "react";
import Image from "next/image";

const Blinds: React.FC = () => {
  const blindTypes = [
    {
      id: 1,
      name: "Roller Blinds",
      description:
        "Sleek and modern roller blinds perfect for contemporary spaces.",
      price: "From $89",
      image: "/images/roller-blinds.jpg",
      features: ["Motorized options", "Blackout available", "Easy maintenance"],
    },
    {
      id: 2,
      name: "Venetian Blinds",
      description:
        "Classic venetian blinds with adjustable slats for perfect light control.",
      price: "From $129",
      image: "/images/venetian-blinds.jpg",
      features: ["Adjustable slats", "Wood & aluminum", "Timeless design"],
    },
    {
      id: 3,
      name: "Roman Blinds",
      description: "Elegant roman blinds that add sophistication to any room.",
      price: "From $149",
      image: "/images/roman-blinds.jpg",
      features: ["Soft fabric folds", "Custom patterns", "Premium finish"],
    },
    {
      id: 4,
      name: "Vertical Blinds",
      description: "Perfect for large windows and sliding doors.",
      price: "From $99",
      image: "/images/vertical-blinds.jpg",
      features: ["Large coverage", "Easy operation", "Commercial grade"],
    },
  ];

  const testimonials = [
    {
      id: 1,
      name: "<PERSON>",
      role: "Interior Designer",
      content:
        "The quality and design of these blinds exceeded my expectations. My clients love them!",
      rating: 5,
    },
    {
      id: 2,
      name: "Michael Chen",
      role: "Homeowner",
      content:
        "Professional installation and beautiful blinds that transformed our living room.",
      rating: 5,
    },
    {
      id: 3,
      name: "Emma Davis",
      role: "Property Manager",
      content:
        "Perfect for our rental properties. Durable and stylish at an affordable price.",
      rating: 5,
    },
  ];

  /* ─────────────────────────────────── view ─────────────────────────────────── */
  return (
    <main className="min-h-screen bg-white text-gray-900">
      {/* ── Hero ─────────────────────────────────────────────────────────────── */}
      <section className="relative flex h-[90vh] items-center justify-center overflow-hidden">
        <Image
          src="/images/blinds-hero.jpg"
          alt="Blinds hero background"
          fill
          priority
          className="object-cover object-center opacity-30"
        />
        <div className="absolute inset-0 bg-gradient-to-br from-blue-50/60 via-white/40 to-purple-50/60 backdrop-blur-md" />
        <div className="relative z-10 px-4 text-center">
          <h1 className="bg-gradient-to-r from-blue-900 to-purple-900 bg-clip-text text-5xl font-extrabold tracking-tight text-transparent md:text-7xl">
            Premium&nbsp;Blinds
          </h1>
          <p className="mx-auto mt-6 max-w-xl text-lg text-gray-700 md:text-xl">
            Transform your space with elegance and precision.
          </p>
          <div className="mt-10 flex flex-wrap justify-center gap-4">
            <button className="rounded-full bg-blue-600 px-8 py-3 font-semibold text-white shadow-md transition hover:bg-blue-700 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-300">
              Explore Collection
            </button>
            <button className="rounded-full border border-blue-600 px-8 py-3 font-semibold text-blue-600 transition hover:bg-blue-600 hover:text-white focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-300">
              Book Consultation
            </button>
          </div>
        </div>
      </section>

      {/* ── Features ─────────────────────────────────────────────────────────── */}
      <section className="px-4 py-24">
        <div className="mx-auto max-w-6xl text-center">
          <h2 className="mb-6 text-3xl font-extrabold md:text-4xl">
            Why Choose Our Blinds?
          </h2>
          <p className="mx-auto max-w-2xl text-gray-600">
            Experience the perfect blend of style, functionality, and quality
            craftsmanship.
          </p>

          <div className="mt-20 grid gap-12 md:grid-cols-3">
            {[
              {
                iconBg: "bg-blue-50",
                iconClr: "text-blue-600",
                title: "Premium Quality",
                text: "Crafted with the finest materials for lasting beauty and durability.",
              },
              {
                iconBg: "bg-purple-50",
                iconClr: "text-purple-600",
                title: "Quick Installation",
                text: "Professional installation completed in just a few hours.",
              },
              {
                iconBg: "bg-blue-50",
                iconClr: "text-blue-600",
                title: "Custom Design",
                text: "Tailored to your exact specifications and style preferences.",
              },
            ].map((f, idx) => (
              <div key={idx} className="text-center">
                <div
                  className={`mx-auto mb-6 flex h-14 w-14 items-center justify-center rounded-full ${f.iconBg}`}
                >
                  <svg
                    className={`h-7 w-7 ${f.iconClr}`}
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={1.5}
                      d="M5 13l4 4L19 7"
                    />
                  </svg>
                </div>
                <h3 className="mb-3 text-lg font-semibold">{f.title}</h3>
                <p className="text-sm text-gray-600">{f.text}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* ── Products ─────────────────────────────────────────────────────────── */}
      <section className="bg-gray-50 px-4 py-24">
        <div className="mx-auto max-w-6xl text-center">
          <h2 className="mb-6 text-3xl font-extrabold md:text-4xl">
            Our Collection
          </h2>
          <p className="mx-auto max-w-2xl text-gray-600">
            Discover our range of premium blinds designed for every style and
            need.
          </p>

          <div className="mt-20 grid gap-8 md:grid-cols-2 lg:grid-cols-4">
            {blindTypes.map((blind) => (
              <div
                key={blind.id}
                className="group overflow-hidden rounded-2xl bg-white shadow-sm transition duration-300 hover:scale-[1.02] hover:shadow-xl"
              >
                <div className="relative h-48 overflow-hidden">
                  <Image
                    src={blind.image}
                    alt={blind.name}
                    fill
                    sizes="(min-width:1024px) 25vw, (min-width:768px) 40vw, 100vw"
                    className="object-cover object-center transition-transform duration-500 group-hover:scale-105"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/40 to-transparent" />
                  <div className="absolute bottom-4 left-4 z-10 text-white">
                    <h3 className="text-lg font-semibold">{blind.name}</h3>
                    <p className="text-sm">{blind.price}</p>
                  </div>
                </div>

                <div className="p-6">
                  <p className="mb-4 text-sm text-gray-600">
                    {blind.description}
                  </p>
                  <ul className="mb-6 space-y-1 text-xs text-gray-500">
                    {blind.features.map((feature, i) => (
                      <li key={i} className="flex items-center gap-2">
                        <span className="h-1.5 w-1.5 rounded-full bg-blue-500" />
                        {feature}
                      </li>
                    ))}
                  </ul>
                  <button className="w-full rounded-full bg-blue-600 py-2 text-sm font-medium text-white transition hover:bg-blue-700">
                    View Details
                  </button>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* ── Testimonials ─────────────────────────────────────────────────────── */}
      <section className="px-4 py-24">
        <div className="mx-auto max-w-6xl text-center">
          <h2 className="mb-6 text-3xl font-extrabold md:text-4xl">
            What Our Customers Say
          </h2>
          <p className="mx-auto max-w-2xl text-gray-600">
            Don&apos;t just take our word for it – hear from our satisfied
            customers.
          </p>

          <div className="mt-20 grid gap-8 md:grid-cols-3">
            {testimonials.map((t) => (
              <div
                key={t.id}
                className="rounded-2xl bg-white p-8 shadow-md transition hover:shadow-lg"
              >
                <div className="mb-4 flex items-center gap-1">
                  {[...Array(t.rating)].map((_, i) => (
                    <svg
                      key={i}
                      className="h-4 w-4 text-yellow-400"
                      fill="currentColor"
                      viewBox="0 0 20 20"
                    >
                      <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                    </svg>
                  ))}
                </div>
                <p className="mb-6 text-sm italic text-gray-600">
                  “{t.content}”
                </p>
                <p className="font-semibold">{t.name}</p>
                <p className="text-xs text-gray-500">{t.role}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* ── CTA ──────────────────────────────────────────────────────────────── */}
      <section className="bg-gradient-to-br from-blue-600 to-purple-600 px-4 py-24 text-center text-white">
        <h2 className="mb-6 text-3xl font-extrabold md:text-4xl">
          Ready to Transform Your Space?
        </h2>
        <p className="mx-auto mb-10 max-w-xl text-lg opacity-90">
          Get a free consultation and quote for your perfect blinds today.
        </p>
        <div className="flex flex-wrap justify-center gap-4">
          <button className="rounded-full bg-white px-8 py-3 font-semibold text-blue-600 transition hover:bg-gray-100">
            Get Free Quote
          </button>
          <button className="rounded-full border border-white px-8 py-3 font-semibold transition hover:bg-white hover:text-blue-600">
            Schedule Consultation
          </button>
        </div>
      </section>
    </main>
  );
};

export default Blinds;
