{"name": "metblinds", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@glidejs/glide": "^3.7.1", "@heroicons/react": "^2.2.0", "@heroui/react": "^2.7.8", "@heroui/shared-utils": "^2.1.9", "@hookform/resolvers": "^4.1.2", "@radix-ui/react-slot": "^1.1.2", "@reduxjs/toolkit": "^2.6.0", "axios": "^1.8.3", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "embla-carousel-react": "^8.6.0", "hamburger-react": "^2.5.2", "jodit-react": "^5.2.15", "js-cookie": "^3.0.5", "lucide-react": "^0.477.0", "next": "^15.1.7", "next-redux-wrapper": "^8.1.0", "nprogress": "^0.2.0", "react": "^19.1.0", "react-dom": "^19.1.0", "react-hook-form": "^7.54.2", "react-hot-toast": "^2.5.2", "react-icons": "^5.5.0", "react-markdown": "^10.1.0", "react-modal": "^3.16.3", "react-redux": "^9.2.0", "react-toastify": "^11.0.5", "remark-breaks": "^4.0.0", "remark-gfm": "^4.0.1", "swiper": "^11.2.9", "tailwind-merge": "^3.0.2", "tailwindcss-animate": "^1.0.7", "zod": "^3.24.2"}, "devDependencies": {"@eslint/eslintrc": "^3", "@types/glidejs__glide": "^3.6.6", "@types/js-cookie": "^3.0.6", "@types/jsonwebtoken": "^9.0.9", "@types/node": "^20", "@types/nprogress": "^0.2.3", "@types/react": "^19", "@types/react-dom": "^19", "@types/react-modal": "^3.16.3", "eslint": "^9", "eslint-config-next": "15.1.7", "postcss": "^8", "prettier": "^3.5.2", "prettier-plugin-tailwindcss": "^0.6.11", "tailwindcss": "^3.4.1", "typescript": "^5"}}