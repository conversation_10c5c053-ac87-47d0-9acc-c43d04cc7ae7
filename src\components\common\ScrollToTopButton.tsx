"use client";
import { useState, useEffect } from "react";
import { FaArrowUp } from "react-icons/fa";
import { scrollToTop } from "@/hooks/useScroll";

interface ScrollToTopButtonProps {
  /**
   * Threshold in pixels to show the button (default: 300)
   */
  showAtPosition?: number;
  /**
   * Button position from the edge (default: 20px)
   */
  position?: {
    bottom?: string;
    right?: string;
  };
  /**
   * Button size (default: 40px)
   */
  size?: string;
  /**
   * Button style customization
   */
  className?: string;
}

/**
 * A button that appears when the user scrolls down and allows them to scroll back to the top
 */
const ScrollToTopButton: React.FC<ScrollToTopButtonProps> = ({
  showAtPosition = 300,
  position = { bottom: "20px", right: "20px" },
  size = "40px",
  className = "",
}) => {
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    const handleScroll = () => {
      if (window.scrollY > showAtPosition) {
        setIsVisible(true);
      } else {
        setIsVisible(false);
      }
    };

    // Initial check
    handleScroll();

    // Add scroll listener
    window.addEventListener("scroll", handleScroll);

    // Cleanup
    return () => {
      window.removeEventListener("scroll", handleScroll);
    };
  }, [showAtPosition]);

  const handleClick = () => {
    scrollToTop();
  };

  if (!isVisible) return null;

  return (
    <button
      onClick={handleClick}
      className={`fixed flex items-center justify-center rounded-full bg-[#FFA600] text-white shadow-lg transition-all duration-300 hover:scale-110 hover:bg-[#FF8C00] active:scale-95 ${className}`}
      style={{
        bottom: position.bottom,
        right: position.right,
        width: size,
        height: size,
        zIndex: 50,
      }}
      aria-label="Scroll to top"
    >
      <FaArrowUp />
    </button>
  );
};

export default ScrollToTopButton;
