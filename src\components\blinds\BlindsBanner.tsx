"use client";

import { inter } from "@/fonts";
import React from "react";
import { useRouter } from "next/navigation";
import Image from "next/image";

type Props = {
  data: {
    heading: string;
    subHeading: string;
    cta: string;
    background: string;
  };
};

const BlindsBanner: React.FC<Props> = ({ data }) => {
  const { heading, subHeading, cta, background } = data;
  const router = useRouter();

  return (
    <div className="relative w-full overflow-hidden py-[153px] md:py-[183px] lg:py-[213px]">
      <Image
        src={background}
        alt="Blinds banner background"
        fill
        quality={100}
        priority
        style={{
          objectFit: "cover",
          objectPosition: "center",
        }}
        className="z-[-2]"
      />

      <div className="absolute inset-0 z-[-1] bg-black/50" />

      <div className="relative z-10 mx-auto flex max-w-[1550px] flex-col items-start gap-6 px-4 sm:gap-8 md:gap-10">
        <div className="flex w-full flex-col gap-9">
          <h1
            className={`${inter.className} text-3xl font-bold leading-tight text-white sm:text-4xl md:text-5xl lg:text-6xl`}
          >
            {heading}
          </h1>

          <p
            className={`${inter.className} max-w-[629px] text-base leading-relaxed text-white sm:text-lg md:text-[22px]`}
          >
            {subHeading}
          </p>
        </div>

        <button
          className="rounded-[19.46px] bg-[#FFA600] px-6 py-2 text-white transition-colors hover:bg-[#FFB033] sm:px-9 sm:py-3 md:px-14 md:py-4 md:text-lg"
          onClick={() => router.push("/contact-us")}
        >
          {cta}
        </button>
      </div>
    </div>
  );
};

export default BlindsBanner;
