import dynamic from "next/dynamic";
import { Suspense } from "react";
import TopNav from "@/components/navigation/TopNav";
import MainNavbar from "@/components/navigation/MainNavbar";
import Footer from "@/components/navigation/Footer";

// Dynamically import ScrollToTopButton
const ScrollToTopButton = dynamic(
  () => import("@/components/common/ScrollToTopButton"),
);

// Loading fallback component
const LoadingFallback = () => (
  <div className="h-10 w-full animate-pulse bg-gray-100" />
);

const Layout = ({ children }: { children: React.ReactNode }) => {
  return (
    <>
      <Suspense fallback={<LoadingFallback />}>
        <TopNav />
      </Suspense>

      <Suspense fallback={<LoadingFallback />}>
        <MainNavbar />
      </Suspense>

      <main className="pt-[20px] xl:pt-0">
        <Suspense fallback={<LoadingFallback />}>{children}</Suspense>
      </main>

      <Suspense fallback={<LoadingFallback />}>
        <Footer />
      </Suspense>

      <ScrollToTopButton />
    </>
  );
};

export default Layout;
