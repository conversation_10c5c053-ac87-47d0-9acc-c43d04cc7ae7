"use client";

import NProgress from "nprogress";
import { useEffect, Suspense, useCallback } from "react";
import { usePathname, useSearchParams } from "next/navigation";
import "nprogress/nprogress.css";

// Configure NProgress with optimized settings
NProgress.configure({
  minimum: 0.1,
  easing: "ease",
  speed: 500,
  showSpinner: false,
  trickle: true,
  trickleSpeed: 200,
});

function NProgressBarContent() {
  const pathname = usePathname();
  const searchParams = useSearchParams();

  const startProgress = useCallback(() => {
    NProgress.start();
  }, []);

  const completeProgress = useCallback(() => {
    NProgress.done();
  }, []);

  useEffect(() => {
    // Start progress immediately
    startProgress();

    // Complete progress after a small delay to ensure smooth transition
    const timeoutId = setTimeout(() => {
      completeProgress();
    }, 100);

    return () => {
      clearTimeout(timeoutId);
      completeProgress();
    };
  }, [pathname, searchParams, startProgress, completeProgress]);

  return null;
}

export default function NProgressBar() {
  return (
    <Suspense fallback={null}>
      <NProgressBarContent />
    </Suspense>
  );
}
