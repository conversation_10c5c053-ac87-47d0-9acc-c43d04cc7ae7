"use client";

import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import "./ContactForm.css";
import { twMerge } from "tailwind-merge";
import { toast } from "react-toastify";
import { useState } from "react";

// Define the schema for all fields
const formSchema = z.object({
  name: z.string().min(1, "Name is required"),
  email: z.string().email("Invalid email address"),
  location: z.string().optional(),
  product: z.string().optional(),
  inquiry: z.string().optional(),
  source: z.string().optional(),
  phone: z.string().optional(),
  message: z.string().min(1, "Please enter a valid message"),
});

// Infer TypeScript type from schema
type FormData = z.infer<typeof formSchema>;

export default function ContactForm({
  isSmall,
  className,
}: {
  isSmall?: boolean;
  className?: string;
}) {
  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
  } = useForm<FormData>({
    resolver: zodResolver(formSchema),
  });

  // const onSubmit = (data: FormData) => {
  //   console.log(data); // Replace with your submission logic (e.g., API call)
  // };

  const [isSubmitting, setIsSubmitting] = useState(false);
  const [cooldown, setCooldown] = useState(false);

  // const onSubmit = async (data: FormData) => {
  //   if (isSubmitting || cooldown) return;

  //   setIsSubmitting(true);
  //   setCooldown(true); // block for future attempts

  //   // Auto-unblock after 5 seconds
  //   setTimeout(() => setCooldown(false), 5000);
  //   const payload = {
  //     data: {
  //       Name: data.name,
  //       Email: data.email,
  //       Location: data.location,
  //       Product: data.product,
  //       InquiryType: data.inquiry,
  //       HeardAbout: data.source,
  //       Message: data.message,
  //       Phone: data.phone,
  //     },
  //   };

  //   try {
  //     const res = await fetch(
  //       `${process.env.NEXT_PUBLIC_API_URL}/api/contact-forms`,
  //       {
  //         method: "POST",
  //         headers: {
  //           "Content-Type": "application/json",
  //         },
  //         body: JSON.stringify(payload),
  //       },
  //     );

  //     const result = await res.json();

  //     if (res.ok) {
  //       toast("Message sent successfully!");
  //       reset()
  //     } else {
  //       toast("Failed to send. Please try again.");
  //     }
  //   } catch (error) {
  //     toast("Something went wrong. Please try later.");
  //   }

  //   // Reset submission state after request completes
  //   setIsSubmitting(false);
  // };

const onSubmit = async (data: FormData) => {
  if (isSubmitting || cooldown) return;

  setIsSubmitting(true);
  setCooldown(true);

  const payload = {
    data: {
      Name: data.name,
      Email: data.email,
      Location: data.location,
      Product: data.product,
      InquiryType: data.inquiry,
      HeardAbout: data.source,
      Message: data.message,
      Phone: data.phone,
    },
  };

  try {
    const response = await fetch(
      `${process.env.NEXT_PUBLIC_API_URL}/api/contact-forms`,
      {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(payload),
      }
    );

    const result = await response.json();

    if (response.ok) {
      toast.success("Message sent successfully!");
      reset();
    } else {
      toast.error("Failed to send. Please try again.");
    }
  } catch (error) {
    toast.error("Something went wrong. Please try again later.");
  } finally {
    setIsSubmitting(false);
    setCooldown(false);
  }
};


  return (
    <form
      onSubmit={(e) => {
        e.preventDefault();
        if (!isSubmitting && !cooldown) handleSubmit(onSubmit)(e);
      }}
      noValidate
      className={twMerge(
        `grid w-full grid-cols-1 gap-${isSmall ? "3" : "5"} rounded-lg ${isSmall ? "p-3" : "p-4"} md:grid-cols-2`,
        className,
      )}
    >
      {/* Your Name Field */}
      <div>
        <label
          htmlFor="name"
          className={`mb-${isSmall ? "2" : "3"} block text-sm text-[#878787]`}
        >
          Your Name:
        </label>
        <input
          id="name"
          placeholder="Enter Your Name*"
          className={`w-full rounded-[23px] bg-[#EFEFEF] ${isSmall ? "p-3 text-xs" : "p-4 text-sm"}`}
          {...register("name")}
        />
        {errors.name && (
          <p className="mt-1 text-sm text-red-500">{errors.name.message}</p>
        )}
      </div>

      {/* Your Email Address Field */}
      <div>
        <label
          htmlFor="email"
          className={`mb-${isSmall ? "2" : "3"} block text-sm text-[#878787]`}
        >
          Your Email Address:
        </label>
        <input
          id="email"
          placeholder="Enter Your Email Address*"
          className={`w-full rounded-[23px] bg-[#EFEFEF] ${isSmall ? "p-3 text-xs" : "p-4 text-sm"}`}
          {...register("email")}
        />
        {errors.email && (
          <p className="mt-1 text-sm text-red-500">{errors.email.message}</p>
        )}
      </div>

      {/* Phone Number Field */}
      <div>
        <label
          htmlFor="phone"
          className={`mb-${isSmall ? "2" : "3"} block text-sm text-[#878787]`}
        >
          Phone Number:
        </label>
        <input
          id="phone"
          placeholder="Enter Your Phone Number"
          className={`w-full rounded-[23px] bg-[#EFEFEF] ${isSmall ? "p-3 text-xs" : "p-4 text-sm"}`}
          {...register("phone")}
        />
        {errors.phone && (
          <p className="mt-1 text-sm text-red-500">{errors.phone.message}</p>
        )}
      </div>

      {/* Choose Your Location Dropdown */}
      <div>
        <label
          htmlFor="location"
          className={`mb-${isSmall ? "2" : "3"} block text-sm text-[#878787]`}
        >
          Choose Your Location:
        </label>
        <select
          id="location"
          className={`w-full rounded-[23px] bg-[#EFEFEF] ${isSmall ? "p-3 text-xs" : "p-4 text-sm"}`}
          defaultValue=""
          {...register("location")}
        >
          <option value="" disabled>
            Select a Location
          </option>
          <option value="calgary">Calgary</option>
          <option value="cochrane">Cochrane</option>
          <option value="airdrie">Airdrie</option>
          <option value="red-deer">Red Deer</option>
          <option value="okotoks">Okotoks</option>
          <option value="medicine-hat">Medicine Hat</option>
          <option value="canmore">Canmore</option>
          <option value="strathmore">Strathmore</option>

          {/* Add  more location options as needed */}
        </select>
        {errors.location && (
          <p className="mt-1 text-sm text-red-500">{errors.location.message}</p>
        )}
      </div>

      {/* Product of Interest Dropdown */}
      <div>
        <label
          htmlFor="product"
          className={`mb-${isSmall ? "2" : "3"} block text-sm text-[#878787]`}
        >
          Product of Interest:
        </label>
        <select
          id="product"
          className={`w-full rounded-[23px] bg-[#EFEFEF] ${isSmall ? "p-3 text-xs" : "p-4 text-sm"}`}
          defaultValue=""
          {...register("product")}
        >
          <option value="" disabled>
            Select a Product
          </option>
       
       <option value="zebra-blinds">Zebra Blinds</option>
      <option value="roller-blinds">Roller Blinds</option>
      <option value="double-roller-blinds">Double Roller Blinds</option>
      <option value="roman-shades">Roman Shades</option>
      <option value="luxury-roman-shades">Luxury Roman Shades</option>
      <option value="drapery">Drapery</option>
      <option value="sunscreen-blinds">Sunscreen Blinds</option>
      <option value="trifold-blinds">Trifold Blinds</option>
      <option value="skylight-blinds">Skylight Blinds</option>
      <option value="honeycomb-blinds">Honeycomb Blinds</option>
      <option value="honeycomb-dnn-blinds">Honeycomb DNN Blinds</option>
      <option value="dream-curtains">Dream Curtains</option>



          {/* Add more product options as needed */}
        </select>
        {errors.product && (
          <p className="mt-1 text-sm text-red-500">{errors.product.message}</p>
        )}
      </div>

      {/* Type of Inquiry Dropdown */}
      <div className="">
        <label
          htmlFor="inquiry"
          className={`mb-${isSmall ? "2" : "3"} block text-sm text-[#878787]`}
        >
          Type of Inquiry:
        </label>
        <select
          id="inquiry"
          className={`w-full rounded-[23px] bg-[#EFEFEF] ${isSmall ? "p-3 text-xs" : "p-4 text-sm"}`}
          defaultValue=""
          {...register("inquiry")}
        >
          <option value="" disabled>
            Select Inquiry Type
          </option>
          <option value="product-availability">Product Availability</option>
          <option value="customization-options">Customization Options</option>
          <option value="warranty-info">Warranty Info</option>
          <option value="customer-service">Customer Service</option>
          <option value="other">Other</option>

          {/* Add more inquiry options as needed */}
        </select>
        {errors.inquiry && (
          <p className="mt-1 text-sm text-red-500">{errors.inquiry.message}</p>
        )}
      </div>

      {/* Where Did You Hear About Us? Dropdown */}
      <div className="col-span-full">
        <label
          htmlFor="source"
          className={`mb-${isSmall ? "2" : "3"} block text-sm text-[#878787]`}
        >
          Where Did You Hear About Us?:
        </label>
        <select
          id="source"
          className={`w-full rounded-[23px] bg-[#EFEFEF] ${isSmall ? "p-3 text-xs" : "p-4 text-sm"}`}
          defaultValue=""
          {...register("source")}
        >
          <option value="" disabled>
            How Did You Hear About Us?
          </option>
          <option value="google-search">Google Search</option>
          <option value="social-media">Social Media</option>
          <option value="friend-family">Friend or Family</option>
          <option value="advertisement">Advertisement</option>
          <option value="other">Other</option>

          {/* Add more source options as needed */}
        </select>
        {errors.source && (
          <p className="mt-1 text-sm text-red-500">{errors.source.message}</p>
        )}
      </div>

      {/* Message Textarea */}
      <div className="col-span-full">
        <label
          htmlFor="message"
          className={`mb-${isSmall ? "2" : "3"} block text-sm text-[#878787]`}
        >
          Message:
        </label>
        <textarea
          id="message"
          placeholder="Enter Your Message"
          className={`max-h-[300px] min-h-[100px] w-full resize-none rounded-[23px] bg-[#EFEFEF] ${isSmall ? "p-3 text-xs" : "p-4 text-sm"}`}
          {...register("message")}
        />
        {errors.message && (
          <p className="mt-1 text-sm text-red-500">{errors.message.message}</p>
        )}
      </div>

      {/* Send Message Button */}
      <div className="col-span-full flex justify-end">
        <button
          type="submit"
          disabled={isSubmitting || cooldown}
          className={`rounded-full bg-[#FFA600] text-white ${
            isSubmitting || cooldown ? "cursor-not-allowed opacity-50" : ""
          } ${
            isSmall
              ? "w-full px-5 py-2 text-sm sm:px-8 sm:py-3 sm:text-base"
              : "px-7 py-2.5 text-base sm:px-11 sm:py-4 sm:text-lg"
          }`}
        >
          {isSubmitting
            ? "Sending..."
            : cooldown
              ? "Please wait..."
              : "Send Message"}
        </button>
      </div>
    </form>
  );
}
