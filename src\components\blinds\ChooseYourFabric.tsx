"use client";
import { useState, useRef } from "react";
import Image from "next/image";
import { Swiper, SwiperSlide } from "swiper/react";
import { Navigation } from "swiper/modules";
import { FaCircleChevronLeft, FaCircleChevronRight } from "react-icons/fa6";
import { rubik } from "@/fonts";

type Props = {
  data: {
    heading: string;
    subHeading: string;
    blackoutHeading: string;
    lightFilteringHeading: string;
    blackoutFabrics: string[];
    lightoutFabrics: string[];
  };
};

function FabricSlider({
  title,
  fabrics,
  currentFabric,
  setCurrentFabric,
}: {
  title: string;
  fabrics: string[];
  currentFabric: string;
  setCurrentFabric: (url: string) => void;
}) {
  const prevRef = useRef<HTMLButtonElement>(null);
  const nextRef = useRef<HTMLButtonElement>(null);

  return (
    <div className="w-full">
      <h3
        className={`${rubik.className} mb-4 text-center text-xl font-semibold text-[#023D64] sm:text-2xl`}
      >
        {title}
      </h3>

      <div className="relative px-8">
        <Swiper
          modules={[Navigation]}
          loop
          slidesPerView="auto" /* each slide keeps its own fixed width */
          spaceBetween={20}
          navigation={{
            prevEl: prevRef.current!,
            nextEl: nextRef.current!,
          }}
          onBeforeInit={(swiper) => {
            if (
              swiper.params.navigation &&
              typeof swiper.params.navigation !== "boolean"
            ) {
              swiper.params.navigation.prevEl = prevRef.current;
              swiper.params.navigation.nextEl = nextRef.current;
            }
          }}
        >
          {fabrics.map((fabric, i) => (
            <SwiperSlide
              key={i}
              className="!w-[140px] shrink-0 cursor-pointer sm:!w-[160px] md:!w-[180px]"
              onClick={() => setCurrentFabric(fabric)}
            >
              <div
                className={`relative aspect-[4/3] w-full overflow-hidden rounded-xl border-2 border-[#A39F9F] transition ${
                  currentFabric === fabric ? "ring-4 ring-[#FFBB3D]" : ""
                }`}
              >
                <Image
                  src={fabric}
                  alt="fabric"
                  fill
                  sizes="(max-width: 640px) 140px, (max-width: 768px) 160px, 180px"
                  className="object-cover"
                />
              </div>
            </SwiperSlide>
          ))}
        </Swiper>

        {/* custom arrows */}
        <button
          ref={prevRef}
          aria-label="Previous"
          className="absolute left-0 top-1/2 z-10 -translate-y-1/2 rounded-full bg-white p-2 text-[#FFBB3D] shadow transition hover:scale-110"
        >
          <FaCircleChevronLeft size={24} />
        </button>
        <button
          ref={nextRef}
          aria-label="Next"
          className="absolute right-0 top-1/2 z-10 -translate-y-1/2 rounded-full bg-white p-2 text-[#FFBB3D] shadow transition hover:scale-110"
        >
          <FaCircleChevronRight size={24} />
        </button>
      </div>
    </div>
  );
}

/* ───────────────────────────── main component ──────────────────────────── */
export default function ChooseYourFabric({ data }: Props) {
  const [currentFabric, setCurrentFabric] = useState(
    data.blackoutFabrics[0] ?? "/placeholder.webp",
  );

  return (
    <div className="mx-auto max-w-screen-xl px-4 py-12 sm:py-16 md:py-20 lg:py-24">
      {/* Heading */}
      <div className="mb-10 text-center">
        <h2
          className={`${rubik.className} mb-4 text-3xl font-semibold text-[#013F68] sm:text-4xl md:text-5xl`}
        >
          {data.heading}
        </h2>
        <p className="mx-auto max-w-3xl text-base text-[#767676] sm:text-lg md:text-xl">
          {data.subHeading}
        </p>
      </div>

      {/* Selected fabric preview */}
      <div className="relative mx-auto mb-16 aspect-[16/9] max-w-5xl overflow-hidden rounded-[20px] border-2 border-[#A39F9F]">
        <Image
          src={currentFabric}
          alt="selected fabric"
          fill
          className="object-cover"
        />
      </div>

      {/* Sliders */}
      <div className="grid grid-cols-1 gap-12 xl:grid-cols-2">
        <FabricSlider
          title={data.blackoutHeading}
          fabrics={data.blackoutFabrics}
          currentFabric={currentFabric}
          setCurrentFabric={setCurrentFabric}
        />
        <FabricSlider
          title={data.lightFilteringHeading}
          fabrics={data.lightoutFabrics}
          currentFabric={currentFabric}
          setCurrentFabric={setCurrentFabric}
        />
      </div>
    </div>
  );
}
