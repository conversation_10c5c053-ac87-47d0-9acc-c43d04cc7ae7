import { useState, useEffect } from "react";

/**
 * Custom hook to get and track window dimensions
 * @returns Current window width and height
 */
export const useWindowSize = () => {
  // Initialize with default values for SSR
  const [windowSize, setWindowSize] = useState({
    width: typeof window !== "undefined" ? window.innerWidth : 0,
    height: typeof window !== "undefined" ? window.innerHeight : 0,
  });

  useEffect(() => {
    // Skip if running on server
    if (typeof window === "undefined") return;

    // Handler to call on window resize
    const handleResize = () => {
      setWindowSize({
        width: window.innerWidth,
        height: window.innerHeight,
      });
    };

    // Add event listener
    window.addEventListener("resize", handleResize);

    // Call handler right away so state gets updated with initial window size
    handleResize();

    // Remove event listener on cleanup
    return () => window.removeEventListener("resize", handleResize);
  }, []); // Empty array ensures effect is only run on mount and unmount

  return windowSize;
};

/**
 * Hook to check if the device is mobile based on window width
 * @param breakpoint - Width threshold to consider mobile (default: 768px)
 * @returns Boolean indicating if device is mobile size
 */
export const useIsMobile = (breakpoint = 768) => {
  const { width } = useWindowSize();
  return width < breakpoint;
};

/**
 * Hook to get a specific CSS media query state
 * @param query - CSS media query string (e.g. '(max-width: 768px)')
 * @returns Boolean indicating if the media query matches
 */
export const useMediaQuery = (query: string) => {
  const [matches, setMatches] = useState(false);

  useEffect(() => {
    if (typeof window === "undefined") return;

    const media = window.matchMedia(query);

    // Update matches state
    const updateMatches = () => {
      setMatches(media.matches);
    };

    // Set initial value
    updateMatches();

    // Add listener for changes
    media.addEventListener("change", updateMatches);

    // Clean up listener
    return () => {
      media.removeEventListener("change", updateMatches);
    };
  }, [query]);

  return matches;
};
