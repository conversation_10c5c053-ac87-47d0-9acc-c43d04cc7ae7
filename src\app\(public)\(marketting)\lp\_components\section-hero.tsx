import { ArrowRightIcon } from "lucide-react";
import { pageData } from "../config";
import LPContactForm from "./lp-contact-form";

export function SectionHero() {
  const { hero } = pageData;

  return (
    <section
      className="h-[calc(100vh-9rem)] w-full bg-cover bg-center bg-no-repeat"
      style={{ backgroundImage: `url('${hero.backgroundImage}')` }}
    >
      <div className="global-container flex h-full items-center justify-center gap-8 py-16 sm:gap-12 md:gap-16 lg:gap-24 xl:gap-36">
        <div className="h-full w-1/2 space-y-6 text-white">
          <div>
            <h4 className="text-2xl font-medium">{hero.specialOffer.badge}</h4>
            <h2
              className="max-w-xl text-7xl font-bold"
              style={{ color: hero.specialOffer.titleColor }}
            >
              {hero.specialOffer.title}
            </h2>
          </div>
          <ul className="list-inside list-disc space-y-2 text-lg marker:text-[#FFA600]">
            {hero.benefits.map((benefit, index) => (
              <li key={index}>{benefit}</li>
            ))}
          </ul>
          <p className="text-3xl font-medium">
            <span className="line-through">{hero.pricing.originalPrice}</span>{" "}
            <span style={{ color: hero.pricing.discountColor }}>
              {hero.pricing.discountedPrice}
            </span>
          </p>
          <p className="max-w-lg text-xl">{hero.description}</p>
          <button
            className="flex items-center gap-2 rounded-3xl px-4 py-2 font-bold"
            style={{
              backgroundColor: hero.ctaButton.bgColor,
              color: hero.ctaButton.textColor,
            }}
          >
            {hero.ctaButton.text} <ArrowRightIcon />
          </button>
          <div className="flex items-start">
            {hero.certificates.map((cert, index) => (
              <img
                key={index}
                width={cert.width}
                src={cert.src}
                alt={cert.alt}
              />
            ))}
          </div>
        </div>
        <div className="flex h-full w-1/2 items-center justify-center">
          <div className="relative h-full w-2/3 space-y-6 rounded-3xl border-2 border-white bg-black/45 p-6">
            <img
              className="absolute right-4 top-0"
              width={hero.formSection.award.width}
              height={hero.formSection.award.height}
              src={hero.formSection.award.src}
              alt={hero.formSection.award.alt}
            />
            <h3 className="max-w-xs text-4xl font-semibold text-white">
              {hero.formSection.title}
            </h3>
            <LPContactForm />
          </div>
        </div>
      </div>
    </section>
  );
}
