import { controlSystemPage } from "@/configs/pages-data/control-systems";
import React from "react";
import { FaCheckCircle } from "react-icons/fa";
import { RiCloseCircleFill } from "react-icons/ri";
import { inter } from "@/fonts";

type Props = {
  data: typeof controlSystemPage.controlSystemTable;
};

const ControlSystemTable: React.FC<Props> = ({ data }) => {
  return (
    <div className="py-24">
      <div className="global-container">
        <div className="mb-12 flex flex-col justify-center">
          <h2
            className={`${inter.className} relative mx-auto mb-6 max-w-[700px] text-center text-3xl text-[#023D64] after:absolute after:left-[45%] after:top-0 after:-z-10 after:h-14 after:w-[140px] after:rounded-full after:bg-[#FFA600] sm:text-4xl md:text-5xl`}
          >
            {data.heading}
          </h2>
          <p className="mx-auto max-w-[1100px] text-center text-sm text-[#767676] sm:text-xl md:text-[22px]">
            {data.subHeading}
          </p>
        </div>

        {/* Table container with scroll indicator */}
        <div className="flex items-center gap-2">
          <div className="hidden shrink-0 sm:block">
            <div className="h-8 w-8 animate-pulse rounded-full border-2 border-[#FDBA3C]">
              <div className="h-full w-full animate-bounce rounded-full bg-[#FDBA3C] opacity-50"></div>
            </div>
          </div>

          <div className="w-full overflow-x-auto">
            <div className="scrollbar-thin scrollbar-thumb-[#FDBA3C] scrollbar-track-gray-100 flex-1 overflow-x-auto rounded-[32px] shadow-md">
              <div className="min-w-[700px] bg-white md:min-w-[1100px] lg:min-w-[1400px]">
                {/* Header */}
                <div className="grid grid-cols-7 overflow-hidden rounded-[32px] bg-[#FDBA3C] px-4 sm:px-8 md:px-14">
                  <div className="overflow-hidden text-ellipsis whitespace-nowrap p-2 text-xs font-medium uppercase tracking-wider text-white sm:p-3 sm:text-sm md:p-4 md:text-base">
                    Blinds
                  </div>
                  {data.controlOptions.map((option) => (
                    <div
                      key={option}
                      className="overflow-hidden text-ellipsis whitespace-nowrap p-2 text-xs font-medium uppercase tracking-wider text-white sm:p-3 sm:text-sm md:p-4 md:text-base"
                    >
                      {option}
                    </div>
                  ))}
                </div>

                {/* Body */}
                <div className="divide-y divide-gray-200 rounded-b-[32px] bg-white px-4 py-4 sm:px-8 sm:py-6 md:px-14 md:py-8">
                  {data.blindsDetails.map((system, index) => (
                    <div
                      key={system.blindType}
                      className={`grid grid-cols-7 ${index === data.blindsDetails.length - 1 ? "rounded-b-[32px]" : ""}`}
                    >
                      <div className="p-2 text-sm font-medium text-[#504D4D] sm:p-3 sm:text-base md:p-4 md:text-lg">
                        {system.blindType}
                      </div>
                      {data.controlOptions.map((option) => (
                        <div
                          key={option}
                          className="flex items-center justify-center p-2 sm:p-3 md:p-4"
                        >
                          {system.controls[
                            option as keyof typeof system.controls
                          ] ? (
                            <FaCheckCircle className="text-xl text-amber-400 sm:text-2xl md:text-3xl" />
                          ) : (
                            <RiCloseCircleFill className="text-xl text-gray-500 sm:text-2xl md:text-3xl" />
                          )}
                        </div>
                      ))}
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>

          <div className="hidden shrink-0 sm:block">
            <div className="h-8 w-8 animate-pulse rounded-full border-2 border-[#FDBA3C]">
              <div className="h-full w-full animate-bounce rounded-full bg-[#FDBA3C] opacity-50"></div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ControlSystemTable;
