"use client";
import React, { useEffect } from "react";
import { rubik } from "@/fonts";
import { IoStar } from "react-icons/io5";

const Testimonials = ({ className }: { className?: string }) => {
  useEffect(() => {
    const script = document.createElement("script");
    script.src = "https://static.elfsight.com/platform/platform.js";
    script.async = true;
    document.body.appendChild(script);
  }, []);
  return (
    <div className={`py-12 md:py-10 ${className}`}>
      <div className="mx-auto max-w-[1560px] px-3 md:px-5">
        <div className="flex flex-col items-center justify-center">
          <h2
            className={`${rubik.className} relative z-0 mb-10 text-center text-3xl font-semibold text-[#013F68] after:absolute after:left-[27%] after:top-0 after:-z-10 after:h-full after:w-[50%] after:rounded-full after:bg-[#FFA600] sm:text-4xl md:text-5xl`}
          >
            THE TRUST WE HAVE EARNED
          </h2>
        </div>

        {/* Showing the rating */}
        <div className="mb-8 flex flex-col items-center justify-center gap-4 md:mb-16">
          <div className="text-center">
            <div className="mb-3 flex items-center justify-center gap-3 md:gap-5">
              <img
                src={"/google.svg"}
                alt="google image"
                width={100}
                height={38}
                className="md:h-[50px] md:w-[131px]"
              />
              <h4 className="text-xl font-semibold text-[#013F68] md:text-[28px]">
                Reviews
              </h4>
            </div>

            <div className="flex flex-col items-center gap-2">
              <div className="flex items-center gap-2">
                {Array.from({ length: 5 }).map((_, index) => (
                  <IoStar
                    color="#FFA600"
                    className="text-2xl transition-transform duration-200 hover:scale-110 md:text-3xl"
                    key={index}
                  />
                ))}
              </div>
              <div>
                <h6 className="text-center text-base text-gray-700 md:text-lg">
                  5 star rating of more than 89 reviews
                </h6>
              </div>
            </div>
          </div>
        </div>

        {/* Elfsight Google Reviews Widget */}
        <div className="relative mb-8 flex h-[500px] items-center justify-center rounded-2xl bg-gray-50 p-4 md:mb-16">
          {/* <div
            dangerouslySetInnerHTML={{
              __html: `
                <!-- Elfsight Google Reviews | Untitled Google Reviews -->
                <script src="https://static.elfsight.com/platform/platform.js" async></script>
                <div class="elfsight-app-a0f7f364-702d-432f-9480-832e921a6775" data-elfsight-app-lazy></div>
              `,
            }}
          /> */}
          <div
      className="elfsight-app-54b376a2-7a3b-42df-8cbb-2baa654c6726"
      data-elfsight-app-lazy
    ></div>
        </div>
      </div>
    </div>
  );
};

export default Testimonials;
