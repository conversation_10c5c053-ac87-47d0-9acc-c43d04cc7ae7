import BlindsBanner from "@/components/blinds/BlindsBanner";
import KeyFeatures from "@/components/blinds/KeyFeatures";
import WhyChooseBlindsSlider from "@/components/blinds/WhyChooseBlindsSlider";
import FAQsSection from "@/components/common/FAQsSection";
import InfoBanner from "@/components/common/InfoBanner";
import ChooseHardware from "@/components/blinds/ChooseHardware";
import ChooseYourFabric from "@/components/blinds/ChooseYourFabric";
import BlindsFeatures from "@/components/blinds/BlindsFeatures";
import ChooseControlSystem from "@/components/blinds/ChooseControlSystem";
import blinds from "@/configs/pages-data/blinds";
import { Metadata } from "next";
import Testimonials from "@/components/common/Testimonials";

export async function generateMetadata({
  params,
}: {
  params: Promise<{ slug: string }>;
}): Promise<Metadata> {
  const resolvedParams = await params;
  const blind = blinds.find((blind) => blind.slug === resolvedParams.slug);

  if (!blind) {
    return {
      title: "Blind Not Found",
      description: "The requested blind could not be found",
    };
  }

  return {
    title: blind.title,
    description: blind.description,
  };
}

const page = async ({ params }: { params: Promise<{ slug: string }> }) => {
  const { slug } = await params;
  const blind = blinds.find((blind) => blind.slug === slug);

  if (!blind) {
    return <div>Blind not found</div>;
  }

  return (
    <div>
      <BlindsBanner data={blind.blindsBanner} />
      <WhyChooseBlindsSlider data={blind.whyChooseBlindsSlider} />
      <KeyFeatures data={blind.keyFeaures} />
      <ChooseYourFabric data={blind.chooseYourFabric} />
      <ChooseHardware data={blind.chooseHardware} />
      <ChooseControlSystem data={blind.chooseControlSystem} />
      <BlindsFeatures data={blind.blindsFeatures} />
      <Testimonials className="lg:mt-16" />
      <FAQsSection data={blind.faqs} />
      <InfoBanner data={blind.infoBanner} />
      <div className="h-10 sm:h-12 md:h-14 lg:h-20 xl:h-28"></div>
    </div>
  );
};

export default page;
export async function generateStaticParams() {
  const slugs = ["post-1", "post-2", "post-3"]; // Example slugs
  return slugs.map((slug) => ({
    slug, // Maps each slug to the [slug] param
  }));
}
