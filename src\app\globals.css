@import "../styles/glide.css";
@tailwind base;
@tailwind components;
@tailwind utilities;

/* NProgress custom styling */
#nprogress .bar {
  background: #ffa600 !important;
  height: 3px !important;
}

#nprogress .peg {
  box-shadow:
    0 0 10px #ffa600,
    0 0 5px #ffa600 !important;
}

.global-container {
  @apply mx-auto max-w-[1800px] px-3 sm:px-6;
}

html {
  scroll-behavior: smooth;
}

/* Hide swiper navigation buttons on small screens */
@media (max-width: 767px) {
  /* Hide buttons in BlindsShowcase, Testimonials, and BlindsTestimonials components */
  .relative.flex.items-center button[class*="text-3xl"],
  .relative.flex.h-\[500px\].items-center button[class*="text-3xl"],
  .glide__arrows button,
  /* Specifically target customer review components */
  div:has(.swiper) > button[class*="text-4xl"],
  .relative.flex.h-\[500px\].items-center > button,
  .relative.flex.items-center.gap-2 > button {
    display: none !important;
  }

  /* Exception for hardware carousel - keep those arrows visible */
  .hardware-carousel button,
  .hardware-nav-btn {
    display: flex !important;
    visibility: visible !important;
    opacity: 1 !important;
    min-width: 40px !important;
    min-height: 40px !important;
    font-size: 1.5rem !important;
  }

  .hardware-nav-icon {
    display: block !important;
    font-size: 1.5rem !important;
  }
}

/* Custom Swiper Pagination Styling */
.swiper-pagination-bullet {
  width: 10px !important;
  height: 10px !important;
  background: rgba(
    255,
    166,
    0,
    0.5
  ) !important; /* Using #FFA600 with opacity */
  opacity: 1 !important;
}

.swiper-pagination-bullet-active {
  background: #ffa600 !important; /* #FFA600 - matches your theme color */
}

/* Make pagination more visible on mobile */
@media (max-width: 767px) {
  .swiper-pagination {
    position: relative !important;
    bottom: 0 !important;
    margin-top: 15px !important;
  }

  .swiper-pagination-bullet {
    width: 12px !important;
    height: 12px !important;
    margin: 0 6px !important;
  }
}

html {
  scroll-behavior: smooth;
}
