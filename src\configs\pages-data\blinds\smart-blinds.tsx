import React from "react";
import { WiDaySunny } from "react-icons/wi";
import { MdSecurity, MdOutlineDesignServices } from "react-icons/md";
import { BsLightningChargeFill } from "react-icons/bs";
import { FaRegClock, FaShieldAlt } from "react-icons/fa";
import { FiSun } from "react-icons/fi";
import { CiWallet } from "react-icons/ci";
import { FaRegCircleCheck } from "react-icons/fa6";

const smartBlinds = {
  slug: "smart-blinds",
  title: "Smart Blinds",
  icon: "/icons/control-systems/smart-control.svg",
  description:
    "Smart Blinds  allows you to operate your blinds using smartphone apps, voice commands, and home automation systems.",
  image: "/images/controlsystem/smartcontrol/0.webp",

  blindsBanner: {
    heading: "Why Smart Blinds Might Be Perfect for You?",
    subHeading:
      "The Smart Binds  revolutionizes window treatment automation for modern homes. Now, adjust your privacy and ambience using a smartphone app, voice command, or an existing Smart Home system. A must-have solution for those seeking ultimate convenience and control.",
    cta: "Request free Quote",
    background: "/images/controlsystem/smartcontrol/banner.webp",
  },
  /**
   * why choose blinds slider
   */
  whyChooseBlindsSlider: {
    heading: "Why Choose Smart Blinds",
    features: [
      {
        icon: <FaRegClock />,
        title: "Strong and durable manual control",
        image: "/images/controlsystem/smartcontrol/0.webp",
      },
      {
        icon: <FiSun />,
        title: "Precise light and privacy control",
        image: "/images/controlsystem/smartcontrol/1.webp",
      },
      {
        icon: <FaShieldAlt />,
        title: "Child-safe lock mechanism prevents hazards",
        image: "/images/controlsystem/smartcontrol/2.webp",
      },
      {
        icon: <CiWallet />,
        title: "Budget-friendly and easy to maintain",
        image: "/images/controlsystem/smartcontrol/3.webp",
      },
      {
        icon: <FaRegCircleCheck />,
        title: "Available in PVC, fabric, and stainless steel cord options",
        image: "/images/controlsystem/smartcontrol/4.webp",
      },
    ],
  },
  /**
   * key features
   */
  keyFeaures: {
    heading: "KEY FEATURES",
    image: "/images/blinds/SkylightBlinds/features.webp",
    features: [
      {
        title: "Premium Material",
        description:
          "Choose between various fabric blends, PVC, wood, and aluminum",
      },
      {
        title: "Custom Light Adjustment",
        description: "Light filtering, total blackout, UV protection",
      },
      {
        title: "Smart Integration",
        description:
          "Control the blinds through Google Home, Alexa, and Apple HomeKit",
      },
      {
        title: "Versatile",
        description: "Manual, cordless, motorized, or SmartHome compatibility",
      },
    ],
  },
  /**
   * choose your fabric
   */
  chooseYourFabric: {
    heading: "Choose Your Fabric",
    subHeading:
      "No more compromise with your style pick from a wide range of textures and colors to compliment your interior.",
    blackoutHeading: "Blackout Fabrics",
    lightFilteringHeading: "Light Filtering Fabrics",
    blackoutFabrics: [
      "/images/blinds/fabrics/skylight/dark/5.webp",
      "/images/blinds/fabrics/skylight/dark/6.webp",
      "/images/blinds/fabrics/skylight/dark/8.webp",
      "/images/blinds/fabrics/skylight/dark/9.webp",
      "/images/blinds/fabrics/skylight/dark/10.webp",
    ],
    lightoutFabrics: [
      "/images/blinds/fabrics/skylight/light/1.webp",
      "/images/blinds/fabrics/skylight/light/2.webp",
      "/images/blinds/fabrics/skylight/light/3.webp",
      "/images/blinds/fabrics/skylight/light/4.webp",
      "/images/blinds/fabrics/skylight/light/7.webp",
    ],
  },
  /**
   * choose hardware
   */
  chooseHardware: {
    heading: "Choose Your Hardware",
    subHeading:
      "Exquisite hardware material that matches your decor style, without sacrificing the luxury.",
    hardwareOptions: [
      {
        image: "/images/hardwares/hardware-1.webp",
        name: "Antique Brass",
      },
      {
        image: "/images/hardwares/hardware-2.webp",
        name: "Satin Nickel",
      },

      {
        image: "/images/hardwares/hardware-4.webp",
        name: "Polished Chrome",
      },
    ],
  },
  /**
   * choose control system
   */
  chooseControlSystem: {
    heading: "Choose Your Control System",
    subHeading:
      "Enhance your experience with a motorized smart control system adjust your retractable blinds with a remote, voice control, or smartphone app.",
    buttonText: "Request  free Quote",
  },
  /**
   * blinds features
   */
  blindsFeatures: {
    heading: "How It Works?",
    image: "/images/blinds/SkylightBlinds/How It Works_.webp",
    features: [
      {
        icon: "/svgs/easy-operation.svg",
        title: "Remote Control",
      },
      {
        icon: "/svgs/easy-to-manage.svg",
        title: "Easy Maintenance",
      },
      {
        icon: "/svgs/customizability.svg",
        title: "Customizable",
      },
      {
        icon: "/svgs/versatility.svg",
        title: "Versatile Design",
      },
    ],
  },
  /**
   * blinds testimonials
   */
  blindsTestimonials: {
    heading: "Customer Reviews",
    reviews: [
      {
        userImage: "/profile-image.webp",
        userName: "John Smith",
        rating: 5,
        date: "2023-10-15",
        comment:
          "Perfect for my skylights. The remote control is so convenient!",
      },
      {
        userImage: "/profile-image.webp",
        userName: "Emma Wilson",
        rating: 4,
        date: "2023-11-20",
        comment: "Great quality and the installation was professional.",
      },
      {
        userImage: "/profile-image.webp",
        userName: "Michael Brown",
        rating: 5,
        date: "2023-11-05",
        comment: "Love how they control the light from above.",
      },
      {
        userImage: "/profile-image.webp",
        userName: "Sarah Chen",
        rating: 5,
        date: "2023-10-28",
        comment: "The motorized option is a game-changer. So convenient!",
      },
      {
        userImage: "/profile-image.webp",
        userName: "David Lee",
        rating: 4,
        date: "2023-10-22",
        comment: "Perfect for my sunroom. The UV protection is great.",
      },
    ],
  },
  /**
   * FAQs
   */
  faqs: {
    heading: "FAQs",
    faqs: [
      {
        question: "Do Smart Blinds help with temperature control?",
        answer:
          "Yes. Smart Blinds block the heat in summer and retain warmth in winter, controlling the temperature naturally.",
      },
      {
        question: "Are Smart Blinds waterproof?",
        answer:
          "Yes, certain Smart Blinds are made from moisture-resistant materials, making them perfect for humid spaces like bathrooms and kitchens.",
      },
      {
        question: "Can I motorize the Smart Blinds?",
        answer:
          "Absolutely. (Motorized) Automatic skylight covers can be used via remote control, Google Home, Alexa, etc a perfect solution for hard-to-reach windows.",
      },
      {
        question: "How to cover a skylight without blinds?",
        answer:
          "If you don’t want to use blinds, drapes or curtains can be an ideal solution to cover the skylight.",
      },
    ],
  },

  /**
   * info banner
   */
  infoBanner: {
    heading: "Ready to Transform Your Space?",
    backgroundImage: "/images/home/<USER>",
    points: [
      {
        text: "Add a touch of elegance to your home’s aesthetic with our Skylight Window Shades. Schedule a free consultation and our experts will make a perfect recommendation suited to your interior aesthetics and style.",
      },
    ],
  },
};

export default smartBlinds;
