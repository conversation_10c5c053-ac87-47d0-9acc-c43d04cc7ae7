"use client";

import { useState, useEffect } from "react";
import Image from "next/image";
import { roboto } from "@/fonts";
import {
  Carousel,
  CarouselContent,
  CarouselItem,
  CarouselNext,
  CarouselPrevious,
} from "@/components/ui/carousel";
import {
  FaCircleChevronLeft,
  FaCircleChevronRight,
  FaCheck,
  FaTag,
} from "react-icons/fa6";
import { type CarouselApi } from "@/components/ui/carousel";
import { FaInfoCircle } from "react-icons/fa";

interface HardwareOption {
  image: string;
  name: string;
  description?: string;
  features?: string[];
  price?: string;
}

type Props = {
  data: {
    heading: string;
    subHeading: string;
    hardwareOptions: HardwareOption[];
  };
};

const defaultHardwareOptions: HardwareOption[] = [
  {
    name: "Premium Black Hardware",
    image: "/hardware-black.webp",
    description: "Sleek black finish with premium materials for a modern look",
    features: [
      "Remote control compatible",
      "Voice control ready",
      "5-year warranty",
    ],
    price: "$149.99",
  },
  {
    name: "Brushed Nickel Hardware",
    image: "/hardware-silver.webp",
    description: "Elegant brushed nickel finish that complements any decor",
    features: ["Smart home integration", "Quiet operation", "Energy efficient"],
    price: "$129.99",
  },
];

const ChooseHardware = ({ data }: Props) => {
  const hardwareOptions = data?.hardwareOptions?.length
    ? data.hardwareOptions
    : defaultHardwareOptions;

  const [selectedImage, setSelectedImage] = useState(
    hardwareOptions[0]?.image || "/placeholder-image.webp",
  );
  const [selectedIndex, setSelectedIndex] = useState(0);
  const [api, setApi] = useState<CarouselApi>();
  const [current, setCurrent] = useState(0);
  const [count, setCount] = useState(0);
  const [isMobile, setIsMobile] = useState(false);

  const selectedHardware =
    hardwareOptions[selectedIndex] ||
    hardwareOptions[0] ||
    defaultHardwareOptions[0];

  useEffect(() => {
    const checkIfMobile = () => setIsMobile(window.innerWidth < 640);
    checkIfMobile();
    window.addEventListener("resize", checkIfMobile);
    return () => window.removeEventListener("resize", checkIfMobile);
  }, []);

  useEffect(() => {
    if (!api) return;
    setCount(api.scrollSnapList().length);
    setCurrent(api.selectedScrollSnap());
    api.on("select", () => {
      const newIndex = api.selectedScrollSnap();
      setCurrent(newIndex);
      if (isMobile) {
        setSelectedImage(
          hardwareOptions[newIndex]?.image || "/placeholder-image.webp",
        );
        setSelectedIndex(newIndex);
      }
    });
  }, [api, hardwareOptions, isMobile]);

  const handleSlideClick = (image: string, index: number) => {
    setSelectedImage(image);
    setSelectedIndex(index);
    if (isMobile && api) api.scrollTo(index);
  };

  const getGridColumns = () => "basis-1/2 sm:basis-1/3 lg:basis-1/3";

  return (
    <div className="bg-gradient-to-b from-white to-gray-50 py-8 sm:py-12 md:py-16 lg:py-24">
      <div className="mx-auto max-w-[1560px] px-4 sm:px-6">
        {/* Heading */}
        <div className="relative mb-8 text-center md:mb-12">
          <div className="relative inline-block">
            <div className="absolute -right-12 -top-3 z-10 rotate-12 transform rounded-full bg-[#FFA600] px-3 py-1 text-xs font-bold text-white shadow-md sm:px-4 sm:py-1.5 sm:text-sm">
              NEW
            </div>
            <h2
              className={`${roboto.className} relative z-0 inline-block text-xl font-semibold text-[#023D64] after:absolute after:left-[40%] after:top-0 after:-z-10 after:h-8 after:w-[80px] after:rounded-full after:bg-[#FFA600] sm:text-2xl sm:after:h-12 sm:after:w-[120px] md:text-3xl md:after:h-14 md:after:w-[140px]`}
            >
              {data?.heading || "Choose Your Hardware"}
            </h2>
          </div>
          <p className="mx-auto mt-3 max-w-2xl text-sm text-[#767676] sm:text-base md:text-lg">
            {data?.subHeading ||
              "Select from our range of high-quality hardware options to customize your blinds"}
          </p>
        </div>

        <div className="flex flex-col items-center gap-8 lg:gap-16 xl:flex-row xl:items-start">
          {/* Left: Selected Image & Info */}
          <div className="relative order-2 w-full xl:order-1 xl:w-[828px] xl:flex-1">
            <div className="relative h-[250px] w-full overflow-hidden rounded-[20px] bg-white shadow-md sm:h-[350px] md:h-[450px] md:rounded-[30px] lg:h-[493px] xl:rounded-[49px]">
              <Image
                src={selectedImage}
                alt={selectedHardware.name}
                fill
                quality={90}
                className="object-cover transition-all duration-300"
                sizes="(max-width: 768px) 100vw, 800px"
              />

              <div className="absolute left-4 top-4 flex items-center space-x-1 rounded-lg bg-[#023D64] px-3 py-1 text-white shadow-lg sm:px-4 sm:py-2">
                <span className="text-xs font-semibold sm:text-sm">
                  {selectedHardware.name}
                </span>
              </div>

              {selectedHardware.price && (
                <div className="absolute bottom-4 right-4 flex items-center space-x-1 rounded-lg bg-[#FFA600] px-3 py-1 text-white shadow-lg sm:px-4 sm:py-2">
                  <FaTag className="mr-1" />
                  <span className="text-xs font-bold sm:text-sm">
                    {selectedHardware.price}
                  </span>
                </div>
              )}
            </div>

            {selectedHardware.description && (
              <div className="relative mt-4 rounded-lg border border-gray-100 bg-white p-4 shadow-sm">
                <FaInfoCircle className="absolute left-4 top-4 text-[#023D64]" />
                <p className="pl-7 text-sm text-gray-600">
                  {selectedHardware.description}
                </p>
              </div>
            )}
          </div>

          {/* Right: Carousel and Features */}
          <div className="order-1 w-full xl:order-2 xl:w-[673px] xl:flex-shrink">
            <div className="mb-6 rounded-xl border border-gray-100 bg-white p-5 shadow-sm">
              <h3 className="mb-4 text-lg font-medium text-[#023D64] sm:text-xl">
                Select Hardware Style
              </h3>

              {hardwareOptions.length > 0 ? (
                <div className="space-y-3 sm:space-y-4">
                  <Carousel
                    opts={{
                      align: "start",
                      loop: hardwareOptions.length > 3,
                      containScroll: "trimSnaps",
                    }}
                    setApi={setApi}
                    className="flex w-full items-center gap-2 sm:gap-3 md:gap-5 [&>*:nth-child(2)]:flex-grow"
                  >
                    <CarouselPrevious className="!static rounded-full bg-white text-xl text-[#FFBB3D] shadow-sm duration-300 hover:scale-105 hover:text-[#FFA600] hover:opacity-100 sm:text-2xl md:text-3xl">
                      <FaCircleChevronLeft />
                    </CarouselPrevious>
                    <CarouselContent className="-ml-2 sm:-ml-3 md:-ml-4">
                      {hardwareOptions.map((option, index) => (
                        <CarouselItem key={index} className={getGridColumns()}>
                          <div className="flex justify-center p-1 sm:p-2">
                            <div
                              className={`relative aspect-square h-auto w-full cursor-pointer overflow-hidden rounded-[15px] border-2 transition-all duration-300 sm:aspect-[184/154] sm:h-[150px] sm:w-[120px] md:h-[170px] md:w-[140px] md:rounded-[24px] lg:h-[184px] lg:w-[154px] ${
                                selectedIndex === index
                                  ? "scale-105 border-[#FFA600] shadow-md"
                                  : "border-transparent hover:border-[#FFA600]/50"
                              }`}
                              onClick={() =>
                                handleSlideClick(option.image, index)
                              }
                            >
                              <Image
                                src={option.image}
                                alt={
                                  option.name || `Hardware option ${index + 1}`
                                }
                                fill
                                className="object-cover"
                                sizes="150px"
                                quality={80}
                              />
                              {selectedIndex === index && (
                                <div className="absolute right-2 top-2 rounded-full bg-[#FFA600] p-1 shadow-sm">
                                  <FaCheck className="text-xs text-white" />
                                </div>
                              )}
                            </div>
                          </div>
                        </CarouselItem>
                      ))}
                    </CarouselContent>
                    <CarouselNext className="!static rounded-full bg-white text-xl text-[#FFBB3D] shadow-sm duration-300 hover:scale-105 hover:text-[#FFA600] hover:opacity-100 sm:text-2xl md:text-3xl">
                      <FaCircleChevronRight />
                    </CarouselNext>
                  </Carousel>

                  <div className="flex justify-center gap-1.5 py-2">
                    {hardwareOptions.map((_, index) => (
                      <button
                        key={index}
                        className={`h-2 w-2 rounded-full transition-all duration-300 sm:h-2.5 sm:w-2.5 ${
                          selectedIndex === index
                            ? "scale-110 bg-[#FFA600]"
                            : "bg-[#FFA600]/30 hover:bg-[#FFA600]/60"
                        }`}
                        onClick={() => {
                          if (api) api.scrollTo(index);
                          setSelectedIndex(index);
                          setSelectedImage(
                            hardwareOptions[index]?.image ||
                              "/placeholder-image.webp",
                          );
                        }}
                        aria-label={`Select hardware option ${index + 1}`}
                      />
                    ))}
                  </div>
                </div>
              ) : (
                <div className="rounded-lg bg-gray-50 py-8 text-center text-gray-500 shadow-sm">
                  No hardware options available
                </div>
              )}
            </div>

            {selectedHardware.features &&
              selectedHardware.features.length > 0 && (
                <div className="rounded-xl border border-gray-100 bg-white p-5 shadow-sm">
                  <h3 className="mb-3 text-lg font-medium text-[#023D64] sm:text-xl">
                    Features
                  </h3>
                  <ul className="space-y-2">
                    {selectedHardware.features.map((feature, index) => (
                      <li key={index} className="flex items-start">
                        <div className="mt-0.5 flex h-5 w-5 flex-shrink-0 items-center justify-center rounded-full bg-[#FFA600]/10">
                          <FaCheck className="text-xs text-[#FFA600]" />
                        </div>
                        <span className="ml-2 text-sm text-gray-600">
                          {feature}
                        </span>
                      </li>
                    ))}
                  </ul>
                  <div className="mt-6">
                    <button className="w-full rounded-lg bg-[#023D64] px-4 py-2.5 text-white shadow-sm transition duration-200 hover:bg-[#023D64]/90">
                      Select This Hardware
                    </button>
                  </div>
                </div>
              )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default ChooseHardware;
