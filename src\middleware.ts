import { NextResponse } from "next/server";
import type { NextRequest } from "next/server";

export function middleware(request: NextRequest) {
  const token = request.cookies.get("token");
  const isAuthenticated = request.cookies.get("isAuthenticated");

  // If not authenticated, redirect to login
  if (!token || isAuthenticated?.value !== "true") {
    const loginUrl = new URL("/login", request.url);
    loginUrl.searchParams.set("redirect", request.nextUrl.pathname);
    return NextResponse.redirect(loginUrl);
  }

  // ✅ REMOVE this redirect
  // if (request.nextUrl.pathname === "/admin") {
  //   return NextResponse.redirect(new URL("/admin/blogs", request.url));
  // }

  return NextResponse.next();
}

export const config = {
  matcher: ["/admin/:path+"],
};
