import { rubik } from "@/fonts";
import React from "react";
import { FaCheck } from "react-icons/fa6";

type Props = {
  data: {
    heading: string;
    image: string;
    features: { title: string; description: string }[];
  };
};

const KeyFeatures: React.FC<Props> = ({ data }) => {
  return (
    <section className="py-8 sm:py-10 md:py-14 lg:py-16 xl:py-20">
      <div className="mx-auto max-w-[1586px] px-3 sm:px-4 md:px-6 lg:px-8">
        <div className="flex flex-col items-center gap-8 md:gap-12 lg:flex-row lg:gap-16 xl:gap-20">
          {/* Content */}
          <div className="order-1 flex w-full flex-1 flex-col items-center space-y-8 sm:space-y-10 md:space-y-12 lg:order-2 lg:items-start">
            {/* Heading */}
            <h2
              className={`${rubik.className} relative z-0 text-center text-2xl font-semibold text-[#013F68] after:absolute after:left-1/2 after:top-0 after:-z-10 after:h-8 after:w-24 after:-translate-x-1/2 after:rounded-full after:bg-[rgb(255,166,0)] sm:text-2xl sm:after:h-10 sm:after:w-32 md:text-3xl md:after:h-12 md:after:w-36 lg:text-left lg:text-4xl lg:after:left-0 lg:after:h-14 lg:after:w-40 lg:after:translate-x-0 xl:text-5xl`}
            >
              {data.heading}
            </h2>
            {/* Features List */}
            <div className="flex w-full flex-col space-y-6 sm:space-y-8 md:space-y-10">
              {data.features.map((feature, index) => (
                <div
                  key={index}
                  className="flex items-start gap-3 sm:gap-4 md:gap-5"
                >
                  <div className="mt-1 flex h-6 w-6 flex-shrink-0 items-center justify-center rounded-full bg-[#FFA600] sm:h-7 sm:w-7 md:h-8 md:w-8">
                    <FaCheck className="h-3.5 w-3.5 text-white sm:h-4 sm:w-4 md:h-5 md:w-5" />
                  </div>
                  <div className="flex-1 break-words text-lg text-[#717171] sm:text-lg md:text-xl lg:text-2xl">
                    <h3 className="inline font-semibold text-black">
                      {feature.title}
                    </h3>
                    {feature.description && (
                      <span className="font-normal text-[#717171]">
                        {" "}
                        - {feature.description}
                      </span>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Image always visible, responsive */}
          <div className="order-2 mb-6 max-h-[400px] min-h-[220px] w-full max-w-xs flex-shrink-0 overflow-hidden rounded-2xl bg-[#D9D9D9] sm:min-h-[300px] sm:max-w-sm md:min-h-[350px] md:max-w-md lg:order-1 lg:mb-0 lg:max-w-md lg:rounded-3xl xl:max-w-lg">
            <img
              src={data.image}
              alt="Contact background"
              className="h-full w-full object-cover"
              // style={{ aspectRatio: "630/788" }}
            />
          </div>
        </div>
      </div>
    </section>
  );
};

export default KeyFeatures;
