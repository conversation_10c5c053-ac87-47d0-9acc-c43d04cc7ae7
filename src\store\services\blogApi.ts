import { createApi, fetchBaseQuery } from "@reduxjs/toolkit/query/react";

type ImageFormat = {
  name: string;
  hash: string;
  ext: string;
  mime: string;
  path: null;
  width: number;
  height: number;
  size: number;
  sizeInBytes: number;
  url: string;
};

type CoverImage = {
  id: number;
  documentId: string;
  name: string;
  alternativeText: string | null;
  caption: string | null;
  width: number;
  height: number;
  formats: {
    thumbnail: ImageFormat;
    small?: ImageFormat;
    medium?: ImageFormat;
    large?: ImageFormat;
  };
  hash: string;
  ext: string;
  mime: string;
  size: number;
  url: string;
  previewUrl: string | null;
  provider: string;
  provider_metadata: any;
  createdAt: string;
  updatedAt: string;
  publishedAt: string;
};

type Block = {
  __component: string;
  id: number;
  content?: string;
  body?: string;
  heading?: string;
  isImageReversed?: boolean;
  imageLandscape?: boolean;
};

type Blog = {
  id: number;
  documentId: string;
  title: string;
  description: string;
  slug: string;
  createdAt: string;
  updatedAt: string;
  publishedAt: string;
  author: string;
  cover: CoverImage;
  blocks: Block[];
};

type GetBlogReq = {
  limit: number | string;
  page: number | string;
};

type StrapiResponse<T> = {
  data: T[];
  meta: {
    pagination: {
      page: number;
      pageSize: number;
      pageCount: number;
      total: number;
    };
  };
};

type StrapiSingleResponse<T> = {
  data: T;
  meta: {
    pagination: {
      page: number;
      pageSize: number;
      pageCount: number;
      total: number;
    };
  };
};

export const blogApi = createApi({
  reducerPath: "blogApi",
  baseQuery: fetchBaseQuery({
    baseUrl:
      `${process.env.NEXT_PUBLIC_API_URL}/api` || "http://localhost:1337/api",
    prepareHeaders: (headers) => {
      return headers;
    },
  }),
  tagTypes: ["Blog"],
  endpoints: (builder) => ({
    /**
     * get all blogs query
     */
    getBlogs: builder.query<StrapiResponse<Blog>, GetBlogReq>({
      query: (params: GetBlogReq) => ({
        url: `/articles?populate=cover&pagination[page]=${params.page}&pagination[pageSize]=${params.limit}`,
        method: "GET",
      }),
      transformResponse: (response: StrapiResponse<Blog>) => ({
        data: response.data,
        meta: response.meta,
      }),
    }),

    /**
     * get blog by slug
     */
    getBlogBySlug: builder.query<StrapiSingleResponse<Blog>, { slug: string }>({
      query: ({ slug }: { slug: string }) => ({
        url:
          `/articles?filters[slug][$eq]=${slug}` +
          `&populate[blocks][on][shared.paragraph-with-image][populate]=*` +
          `&populate[blocks][on][shared.rich-text][populate]=*` +
          `&populate[blocks][on][shared.heading][populate]=*` +
          `&populate[blocks][on][shared.media][populate]=*`,
        method: "GET",
      }),
      transformResponse: (response: StrapiResponse<Blog>) => {
        // Since we're querying by slug, we expect only one result
        const blog = response.data[0];
        return {
          data: blog,
          meta: response.meta,
        };
      },
    }),
  }),
});

export const { useGetBlogsQuery, useGetBlogBySlugQuery } = blogApi;
