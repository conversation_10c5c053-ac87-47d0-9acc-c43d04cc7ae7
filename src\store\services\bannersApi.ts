import { createApi, fetchBaseQuery } from "@reduxjs/toolkit/query/react";

export const bannersApi = createApi({
  reducerPath: "bannersApi",
  baseQuery: fetchBaseQuery({
    baseUrl:
      // `${process.env.NEXT_PUBLIC_API_URL}` || "http://localhost:1337/api",
      "https://api.metblinds.com/api",
  }),
  tagTypes: ["Banner"],
  endpoints: (builder) => ({
    getBanners: builder.query<any, { page: number; limit: number }>({
      query: (params: { page: number; limit: number }) =>
        `/banners?populate=*&pagination[page]=${params.page}&pagination[pageSize]=${params.limit}`,
      transformResponse: (response: any) => ({
        data: {
          data: response.data.map((banner: any) => ({
            id: banner.id,
            background_image: banner.background_image,
            cover_image: banner.cover_image,
            product_offering_headline: banner.offer_headline,
            offer_description: banner.offer_description,
            button_text: banner.button_text,
            redirect_url: banner.button_url,
            post_status: banner.publishedAt ? "published" : "draft",
            discount_percentage: banner.discount_percentage,
            subtitle: banner.subtitle,
          })),
        },
      }),
      forceRefetch: () => true,
    }),
  }),
});

export const { useGetBannersQuery } = bannersApi;
