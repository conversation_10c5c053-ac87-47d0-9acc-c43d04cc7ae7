"use client";
import Link from "next/link";
import { cn, toCapitalized } from "@/lib/utils";
import Image from "next/image";
import { getImageProps } from "@/lib/image-optimization";

interface BannerItemProps {
  banner: {
    backgroundImage?: string;
    coverImage?: string;
    heading?: string;
    subtitle?: string;
    description?: string;
    buttonText?: string;
    link?: string;
    discount_percentage?: string;
  };
  onButtonClick?: () => void;
}

const BannerItem: React.FC<BannerItemProps> = ({ banner, onButtonClick }) => {
  const baseUrl = process.env.NEXT_PUBLIC_API_URL || "";
  const coverImageUrl = banner.coverImage
    ? `${baseUrl}${banner.coverImage}`
    : "";
  const backgroundImageUrl = banner.backgroundImage
    ? `${baseUrl}${banner.backgroundImage}`
    : "";
  const hasBackgroundImage = !!banner.backgroundImage;
  const textColorClass = hasBackgroundImage ? "text-white" : "text-black";

  return (
    <div
      className={cn(
        "relative bg-cover bg-center py-14 max-md:min-h-[550px] md:h-[600px] lg:h-[1000px] lg:py-10",
        !hasBackgroundImage && "bg-white",
      )}
    >
      {hasBackgroundImage && backgroundImageUrl && (
        <>
          <Image
            src={backgroundImageUrl}
            alt="Banner background"
            fill
            className="z-[-1] object-cover object-center"
            {...getImageProps("banner", true)}
          />
          <div className="absolute inset-0 z-[-1] bg-black/50" />
        </>
      )}

      <div className="h-full">
        <div className="mx-auto flex h-full max-w-[1450px] flex-col items-center justify-center gap-8 px-4 md:flex-row md:gap-4 md:px-10 lg:gap-10 lg:px-14 xl:gap-16">
          <div className="banner-text-container flex max-w-[700px] flex-1 flex-col items-center gap-5 text-center md:items-start md:text-left">
            {banner.heading && (
              <h1
                className={`text-4xl font-semibold uppercase md:text-3xl lg:text-6xl ${textColorClass}`}
              >
                {banner.heading}
              </h1>
            )}
            {banner.subtitle && (
              <h2
                className={`text-2xl capitalize md:text-3xl ${textColorClass}`}
              >
                {banner.subtitle}
              </h2>
            )}
            {banner.description && (
              <p
                className={`text-base !capitalize md:text-lg ${textColorClass}`}
              >
                {toCapitalized(banner.description)}
              </p>
            )}
            {banner.buttonText && (
              <Link
                href={banner.link || "#"}
                className="inline-flex items-center justify-center rounded-full bg-[#FFA600] px-5 py-2 text-white transition-colors duration-300 hover:bg-[#FFB733]"
                onClick={onButtonClick}
              >
                <span>{banner.buttonText}</span>
              </Link>
            )}
          </div>

          {coverImageUrl && (
            <div className="relative mb-6 h-[300px] w-full max-w-[450px] md:mr-6 md:h-[350px] md:w-[300px] lg:mb-0 lg:h-[450px] lg:w-[500px]">
              {banner.discount_percentage && (
                <div className="absolute right-0 top-0 z-50 flex aspect-square -translate-y-1/3 translate-x-1/3 items-center justify-center rounded-[50%] bg-white p-5 text-[7px] font-bold text-[#013F68] md:text-[16px] lg:text-[20px]">
                  <span className="text-slate-800">
                    {banner.discount_percentage}
                  </span>
                </div>
              )}
              <Image
                alt="banner image"
                className={cn(
                  "absolute inset-0 mx-auto h-full w-full rounded-xl border-4 border-[#FFA600] object-cover object-center md:border-8",
                  banner.discount_percentage
                    ? "rounded-bl-[120px] rounded-tr-[120px]"
                    : "rounded-tr-[100px]",
                )}
                src={coverImageUrl}
                width={500}
                height={500}
                {...getImageProps("thumbnail", true)}
              />
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default BannerItem;
