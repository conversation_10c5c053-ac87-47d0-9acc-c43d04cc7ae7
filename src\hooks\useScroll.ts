import { useState, useEffect } from "react";

/**
 * Function to smoothly scroll to the top of the page
 * @param options - ScrollToOptions object for controlling behavior
 */
export const scrollToTop = (
  options: ScrollToOptions = { behavior: "smooth" },
) => {
  if (typeof window !== "undefined") {
    window.scrollTo({
      top: 0,
      left: 0,
      ...options,
    });
  }
};

/**
 * Custom hook that provides a scrollToTop function
 * @param options - ScrollToOptions object for controlling behavior
 * @returns Function to call when you want to scroll to top
 */
export const useScrollToTop = (
  options: ScrollToOptions = { behavior: "smooth" },
) => {
  return () => scrollToTop(options);
};

/**
 * Custom hook to track scroll position
 * @param scrollThreshold - Number of pixels to trigger "scrolled" state (default: 5)
 * @returns Object containing scrolled state and lastScrollY position
 */
export const useScrollPosition = (scrollThreshold = 5) => {
  const [scrolled, setScrolled] = useState(false);
  const [lastScrollY, setLastScrollY] = useState(0);

  useEffect(() => {
    // Function to handle scroll events
    const handleScroll = () => {
      // Set scrolled state when scroll exceeds threshold
      setScrolled(window.scrollY > scrollThreshold);
      // Track last scroll position
      setLastScrollY(window.scrollY);
    };

    // Add scroll event listener
    window.addEventListener("scroll", handleScroll);

    // Initialize scroll position
    handleScroll();

    // Clean up event listener on unmount
    return () => window.removeEventListener("scroll", handleScroll);
  }, [scrollThreshold]);

  return {
    scrolled,
    lastScrollY,
    scrollToTop: () => scrollToTop(),
  };
};

/**
 * Custom hook to detect scroll direction
 * @returns Object containing scrollDirection ('up', 'down' or 'none')
 */
export const useScrollDirection = () => {
  const [scrollDirection, setScrollDirection] = useState<
    "up" | "down" | "none"
  >("none");
  const [lastScrollY, setLastScrollY] = useState(0);

  useEffect(() => {
    const updateScrollDirection = () => {
      const scrollY = window.scrollY;
      const direction = scrollY > lastScrollY ? "down" : "up";

      // Only update direction if there's a significant change
      if (scrollY > lastScrollY + 10 || scrollY < lastScrollY - 10) {
        setScrollDirection(direction);
      }

      setLastScrollY(scrollY > 0 ? scrollY : 0);
    };

    window.addEventListener("scroll", updateScrollDirection);

    return () => {
      window.removeEventListener("scroll", updateScrollDirection);
    };
  }, [lastScrollY]);

  return {
    scrollDirection,
    lastScrollY,
    scrollToTop: () => scrollToTop(),
  };
};

/**
 * Hook that tracks whether element is in viewport
 * @param options - IntersectionObserver options
 * @returns Object with ref to attach to element and inView state
 */
export const useInView = (options = {}) => {
  const [ref, setRef] = useState<HTMLElement | null>(null);
  const [inView, setInView] = useState(false);

  useEffect(() => {
    if (ref) {
      const observer = new IntersectionObserver(([entry]) => {
        setInView(entry.isIntersecting);
      }, options);

      observer.observe(ref);

      return () => {
        observer.disconnect();
      };
    }
  }, [ref, options]);

  return {
    ref: setRef,
    inView,
    scrollToTop: () => scrollToTop(),
  };
};
