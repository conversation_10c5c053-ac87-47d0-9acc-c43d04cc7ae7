"use client";
import { bebasNeue } from "@/fonts";
import useScrollToSection from "@/hooks/useScrollToSection";
import { useWindowSize } from "@/hooks/useWindowSize";

type Props = {
  data: {
    heading: string;
    buttonText: string;
    buttonLink: string;
    description?: string;
  };
};

const ProudCanadianFlag = ({ data }: Props) => {
  const { width: windowWidth } = useWindowSize();
  const scrollTo = useScrollToSection();
  // Calculate sizes based on viewport width
  const fontSize =
    windowWidth >= 1400
      ? 115
      : `${Math.min(7.5, Math.max(5, windowWidth * 0.0075))}vw`;
  const imageSize =
    windowWidth >= 1400
      ? 120
      : `${Math.min(10, Math.max(7.5, windowWidth * 0.01))}vw`;
  const buttonPadding =
    windowWidth >= 1400
      ? "16px 48px"
      : `${Math.min(1.4, Math.max(1, windowWidth * 0.0014))}vw ${Math.min(4, Math.max(3, windowWidth * 0.004))}vw`;
  const buttonFontSize =
    windowWidth >= 1400
      ? "24px"
      : `${Math.min(2.2, Math.max(1.5, windowWidth * 0.0022))}vw`;

  return (
    <div className="px-4 py-12 sm:px-6 sm:py-16 md:py-20 lg:py-32">
      <div className="md:rounded-4xl mx-auto max-w-[1400px] overflow-hidden rounded-2xl shadow-lg transition-all duration-300 hover:shadow-xl sm:rounded-3xl lg:rounded-[92px]">
        <div className="bg-white">
          <div className="flex">
            <div className="flex-1 bg-[#EC1A23]"></div>
            <div className="flex flex-1 items-center justify-center p-4 sm:p-6 md:p-8 lg:p-12">
              <img
                src={"/images/canadian-leaf.svg"}
                className="aspect-square max-w-[120px] object-contain transition-transform duration-300 hover:scale-105 sm:max-w-[140px] md:max-w-[160px] lg:max-w-[180px]"
                alt="Canadian Maple Leaf"
                width={120}
                height={120}
                style={{ width: imageSize, height: imageSize }}
              />
            </div>
            <div className="flex-1 bg-[#EC1A23]"></div>
          </div>
          <div className="flex flex-col items-center gap-6 p-6 sm:gap-8 sm:p-8 md:gap-10 md:p-12 lg:gap-14 lg:p-20">
            <h3
              className={`${bebasNeue.className} text-center leading-tight text-[#013F68]`}
              style={{ fontSize }}
            >
              {data.heading}
            </h3>
            <p className="max-w-2xl text-center text-lg text-[#333] md:text-xl">
              {data.description ||
                "We take pride in delivering exceptional quality and service to our fellow Canadians from coast to coast."}
            </p>
            <button
              onClick={() => scrollTo("contact")}
              className="rounded-full bg-[#EC1A23] px-[20px] py-[8px] text-[16px] font-medium text-white transition-all duration-300 hover:scale-105 hover:bg-[#d41720] active:scale-95 md:text-[24px]"
              style={{
                padding: buttonPadding,
              }}
            >
              {data.buttonText}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ProudCanadianFlag;
