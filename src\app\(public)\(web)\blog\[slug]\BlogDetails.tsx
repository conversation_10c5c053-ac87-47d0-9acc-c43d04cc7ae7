"use client";
import { useParams } from "next/navigation";
import { useGetBlogBySlugQuery } from "@/store/services/blogApi";
import { Suspense } from "react";
import ReactMarkdown from "react-markdown";
import remarkBreaks from "remark-breaks";
import Link from "next/link";

const formatDate = (dateString: string) => {
  const options: Intl.DateTimeFormatOptions = {
    day: "2-digit",
    month: "short",
    year: "numeric",
  };
  const date = new Date(dateString);
  return date.toLocaleDateString("en-GB", options);
};

const BlogDetails = () => {
  const params = useParams();
  // getting slug from route params
  const slug = params?.slug as string;

  // getting blog details from api
  const { data, isLoading, error } = useGetBlogBySlugQuery({
    slug,
  });
  console.log("🚀 ~ BlogDetails ~ data:", data);

  if (isLoading)
    return (
      <div className="mx-auto max-w-[1400px] animate-pulse px-3 py-10">
        <div className="mx-auto max-w-[1100px]">
          <div className="mb-4 flex flex-wrap items-center justify-start gap-1.5 text-sm text-[#6E6E73]">
            <span className="h-6 w-20 rounded-md bg-[#F5F5F5] px-2 py-1"></span>
            <span className="h-6 w-20 rounded-md bg-[#F5F5F5] px-2 py-1"></span>
          </div>
          <div className="mb-4">
            <div className="h-10 w-3/4 rounded-md bg-[#F5F5F5]"></div>
          </div>
          <div className="mb-8">
            <div className="aspect-[1194/670] h-auto w-full rounded-[25px] bg-[#F5F5F5]"></div>
          </div>
          <div className="py-10 text-[#3b3b3b]">
            <div className="h-40 rounded-md bg-[#F5F5F5]"></div>
          </div>
        </div>
      </div>
    );

  if (error)
    return (
      <div className="mx-auto max-w-[1400px] px-3 py-16 text-center">
        <div className="mx-auto max-w-[600px]">
          <div className="mb-6 text-red-500">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="mx-auto h-16 w-16"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"
              />
            </svg>
          </div>
          <h2 className="mb-3 text-2xl font-bold">Unable to Load Blog</h2>
          <p className="mb-6 text-[#6E6E73]">
            {` We're having trouble loading this blog post. Please try again later
            or check our other posts.`}
          </p>
          <Link
            href="/blogs"
            className="inline-block rounded-md bg-[#F5F5F5] px-6 py-2 font-medium transition-colors hover:bg-[#e5e5e5]"
          >
            Browse All Blogs
          </Link>
        </div>
      </div>
    );

  if (!data?.data)
    return (
      <div className="mx-auto max-w-[1400px] px-3 py-16 text-center">
        <div className="mx-auto max-w-[600px]">
          <div className="mb-6 text-[#6E6E73]">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="mx-auto h-16 w-16"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"
              />
            </svg>
          </div>
          <h2 className="mb-3 text-2xl font-bold">Blog Not Found</h2>
          <p className="mb-6 text-[#6E6E73]">
            This blog post may have been moved or deleted. Check out our other
            articles instead.
          </p>
          <Link
            href="/blogs"
            className="inline-block rounded-md bg-[#F5F5F5] px-6 py-2 font-medium transition-colors hover:bg-[#e5e5e5]"
          >
            Browse All Blogs
          </Link>
        </div>
      </div>
    );

  const blogDetails = data.data;
  const baseUrl =
    `${process.env.NEXT_PUBLIC_API_URL}` || "http://localhost:1337";

  // Get the best available image format
  const getImageUrl = (cover: any) => {
    if (cover?.formats?.large?.url) return cover.formats.large.url;
    if (cover?.formats?.medium?.url) return cover.formats.medium.url;
    if (cover?.formats?.small?.url) return cover.formats.small.url;
    if (cover?.url) return cover.url;
    return null;
  };

  const imageUrl = getImageUrl(blogDetails.cover);

  return (
    <div>
      <div className="mx-auto max-w-[1400px] px-3 py-10 sm:px-5 md:px-7 lg:px-9 xl:px-12">
        <div className="mx-auto max-w-[1100px]">
          {/* Author and date */}
          <div className="mb-4 flex flex-wrap items-center justify-start gap-1.5 text-sm text-[#6E6E73]">
            <span className="rounded-md bg-[#F5F5F5] px-2 py-1">
              {blogDetails.author}
            </span>
            <span className="rounded-md bg-[#F5F5F5] px-2 py-1">
              {formatDate(blogDetails.publishedAt)}
            </span>
          </div>

          {/* Title */}
          <div className="mb-6">
            <h1 className="text-4xl font-bold">{blogDetails.title}</h1>
          </div>

          {/* Cover image */}
          {imageUrl && (
            <div className="mb-8">
              <img
                src={`${baseUrl}${imageUrl}`}
                alt={blogDetails.cover?.alternativeText || blogDetails.title}
                className="aspect-[1194/670] h-auto w-full rounded-[25px] sm:max-w-[95%] md:max-w-[90%] lg:max-w-[85%] xl:max-w-[80%] 2xl:max-w-[75%]"
              />
            </div>
          )}

          {/* Content blocks */}
          <div className="py-10 text-[#3b3b3b]">
            {blogDetails.blocks?.map((block) => {
              switch (block.__component) {
                case "shared.paragraph-with-image": {
                  const getBlockImageUrl = (img: any) => {
                    if (!img) return null;
                    if (img.formats?.large?.url) return img.formats.large.url;
                    if (img.formats?.medium?.url) return img.formats.medium.url;
                    if (img.formats?.small?.url) return img.formats.small.url;
                    if (img.url) return img.url;
                    return null;
                  };
                  const hasImage = (
                    b: typeof block,
                  ): b is typeof block & { image: any } =>
                    "image" in b && !!b.image;
                  const blockImageUrl = hasImage(block)
                    ? getBlockImageUrl(block.image)
                    : null;
                  const imageAlt = hasImage(block)
                    ? block.image?.alternativeText ||
                      block.heading ||
                      "Block image"
                    : block.heading || "Block image";
                  const isReversed = block.isImageReversed;
                  return (
                    <div
                      key={block.id}
                      className={`mb-8 flex flex-col gap-8 bg-[#f4f4f4] ${
                        isReversed ? "lg:flex-row-reverse" : "lg:flex-row"
                      } items-stretch overflow-hidden`}
                    >
                      {/* Image section */}
                      {blockImageUrl && (
                        <div className="w-full lg:max-w-[60%]">
                          <img
                            src={`${baseUrl}${blockImageUrl}`}
                            alt={imageAlt}
                            className="h-full w-full rounded-xl object-cover"
                            style={{
                              // maxHeight: "100%",
                              objectFit: "fill",
                              objectPosition: "center",
                            }}
                          />
                        </div>
                      )}
                      {/* Paragraph section */}
                      <div className="flex min-w-0 flex-1 items-center p-6">
                        <div className="prose prose-lg w-full">
                          <ReactMarkdown
                            components={{
                              pre: ({ node, ...props }) => (
                                <div className="my-2 w-full overflow-auto rounded-lg bg-black/45 p-2 dark:bg-white/10">
                                  <pre {...props} />
                                </div>
                              ),
                              // Add this to handle paragraphs with proper spacing
                              p: ({ children }) => (
                                <p className="mb-4">{children}</p>
                              ),
                              ul: ({ node, ...props }) => (
                                <ul
                                  className="mb-4 list-disc space-y-1 pl-6"
                                  {...props}
                                />
                              ),
                              ol: ({ node, ...props }) => (
                                <ol
                                  className="mb-4 list-decimal space-y-1 pl-6"
                                  {...props}
                                />
                              ),
                              li: ({ node, ...props }) => (
                                <li className="mb-1" {...props} />
                              ),
                              // Add heading components
                              h1: ({ node, ...props }) => (
                                <h1
                                  className="mb-6 mt-8 text-3xl font-bold"
                                  {...props}
                                />
                              ),
                              h2: ({ node, ...props }) => (
                                <h2
                                  className="mb-4 mt-6 text-2xl font-bold"
                                  {...props}
                                />
                              ),
                              h3: ({ node, ...props }) => (
                                <h3
                                  className="mb-3 mt-4 text-xl font-bold"
                                  {...props}
                                />
                              ),
                              h4: ({ node, ...props }) => (
                                <h4
                                  className="mb-2 mt-3 text-lg font-semibold"
                                  {...props}
                                />
                              ),
                              // Add hyperlink styling
                              a: ({ node, ...props }) => (
                                <a
                                  className="text-blue-600 underline transition-colors hover:text-blue-800"
                                  target="_blank"
                                  rel="noopener noreferrer"
                                  {...props}
                                />
                              ),
                            }}
                            // Add this prop to handle newlines properly
                            remarkPlugins={[remarkBreaks]}
                          >
                            {block.content || ""}
                          </ReactMarkdown>
                        </div>
                      </div>
                    </div>
                  );
                }
                case "shared.rich-text":
                  return (
                    <div
                      key={block.id}
                      className="prose prose-lg mb-8 max-w-none"
                    >
                      <ReactMarkdown
                        components={{
                          pre: ({ node, ...props }) => (
                            <div className="my-2 w-full overflow-auto rounded-lg bg-black/45 p-2 dark:bg-white/10">
                              <pre {...props} />
                            </div>
                          ),
                          // Add this to handle paragraphs with proper spacing
                          p: ({ children }) => (
                            <p className="mb-4">{children}</p>
                          ),
                          ul: ({ node, ...props }) => (
                            <ul
                              className="mb-4 list-disc space-y-1 pl-6"
                              {...props}
                            />
                          ),
                          ol: ({ node, ...props }) => (
                            <ol
                              className="mb-4 list-decimal space-y-1 pl-6"
                              {...props}
                            />
                          ),
                          li: ({ node, ...props }) => (
                            <li className="mb-1" {...props} />
                          ),
                          // Add heading components
                          h1: ({ node, ...props }) => (
                            <h1
                              className="mb-6 mt-8 text-3xl font-bold"
                              {...props}
                            />
                          ),
                          h2: ({ node, ...props }) => (
                            <h2
                              className="mb-4 mt-6 text-2xl font-bold"
                              {...props}
                            />
                          ),
                          h3: ({ node, ...props }) => (
                            <h3
                              className="mb-3 mt-4 text-xl font-bold"
                              {...props}
                            />
                          ),
                          h4: ({ node, ...props }) => (
                            <h4
                              className="mb-2 mt-3 text-lg font-semibold"
                              {...props}
                            />
                          ),
                          // Add hyperlink styling
                          a: ({ node, ...props }) => (
                            <a
                              className="text-blue-600 underline transition-colors hover:text-blue-800"
                              target="_blank"
                              rel="noopener noreferrer"
                              {...props}
                            />
                          ),
                        }}
                        // Add this prop to handle newlines properly
                        remarkPlugins={[remarkBreaks]}
                      >
                        {block.body}
                      </ReactMarkdown>
                    </div>
                  );
                case "shared.heading":
                  return (
                    <h2 key={block.id} className="mb-8 text-3xl font-bold">
                      {block.heading}
                    </h2>
                  );
                case "shared.media": {
                  const getMediaUrl = (file: any) => {
                    if (!file) return null;
                    if (file.formats?.large?.url) return file.formats.large.url;
                    if (file.formats?.medium?.url)
                      return file.formats.medium.url;
                    if (file.formats?.small?.url) return file.formats.small.url;
                    if (file.url) return file.url;
                    return null;
                  };
                  const hasFile = (
                    b: typeof block,
                  ): b is typeof block & { file: any } =>
                    "file" in b && !!b.file;
                  const mediaUrl = hasFile(block)
                    ? getMediaUrl(block.file)
                    : null;
                  const mediaAlt = hasFile(block)
                    ? block.file?.alternativeText || block.file?.name || "Media"
                    : "Media";
                  return mediaUrl ? (
                    <div
                      key={block.id}
                      className="mb-8 flex w-full flex-col items-center"
                    >
                      <img
                        src={`${baseUrl}${mediaUrl}`}
                        alt={mediaAlt}
                        className="w-full max-w-3xl rounded-xl object-cover"
                        style={{ maxHeight: "600px" }}
                      />
                    </div>
                  ) : null;
                }
                default:
                  return null;
              }
            })}
          </div>
        </div>
      </div>
    </div>
  );
};

const BlogDetailsPage = () => {
  return (
    <Suspense fallback={<div>Loading...</div>}>
      <BlogDetails />
    </Suspense>
  );
};

export default BlogDetailsPage;
