"use client";

import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { toast } from "react-toastify";
import { useState } from "react";

// Define the schema for LP form (all fields)
const formSchema = z.object({
  name: z.string().min(1, "Name is required"),
  email: z.string().email("Invalid email address"),
  phone: z.string().optional(),
  location: z.string().optional(),
  product: z.string().optional(),
  inquiry: z.string().optional(),
  source: z.string().optional(),
  message: z.string().min(1, "Please enter a valid message"),
});

// Infer TypeScript type from schema
type FormData = z.infer<typeof formSchema>;

export default function LPContactForm() {
  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
  } = useForm<FormData>({
    resolver: zodResolver(formSchema),
  });

  const [isSubmitting, setIsSubmitting] = useState(false);
  const [cooldown, setCooldown] = useState(false);

  const onSubmit = async (data: FormData) => {
    if (isSubmitting || cooldown) return;

    setIsSubmitting(true);
    setCooldown(true);

    const payload = {
      data: {
        Name: data.name,
        Email: data.email,
        Phone: data.phone,
        Location: data.location,
        Product: data.product,
        InquiryType: data.inquiry,
        HeardAbout: data.source,
        Message: data.message,
        Source: "Landing Page", // Track that this came from the LP
      },
    };

    try {
      const response = await fetch(
        `${process.env.NEXT_PUBLIC_API_URL}/api/contact-forms`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify(payload),
        },
      );

      const result = await response.json();

      if (response.ok) {
        toast.success("Message sent successfully!");
        reset();
      } else {
        toast.error("Failed to send. Please try again.");
      }
    } catch (error) {
      toast.error("Something went wrong. Please try again later.");
    } finally {
      setIsSubmitting(false);
      setCooldown(false);
    }
  };

  return (
    <form
      onSubmit={(e) => {
        e.preventDefault();
        if (!isSubmitting && !cooldown) handleSubmit(onSubmit)(e);
      }}
      noValidate
      className="grid grid-cols-1 gap-3 md:grid-cols-2"
    >
      {/* Your Name Field */}
      <div>
        <label htmlFor="name" className="mb-2 block text-xs text-white/80">
          Your Name:
        </label>
        <input
          id="name"
          placeholder="Enter Your Name*"
          className="w-full rounded-2xl bg-white/90 p-3 text-sm text-gray-800 placeholder-gray-500 focus:bg-white focus:outline-none focus:ring-2 focus:ring-[#FFA600]"
          {...register("name")}
        />
        {errors.name && (
          <p className="mt-1 text-xs text-red-300">{errors.name.message}</p>
        )}
      </div>

      {/* Your Email Address Field */}
      <div>
        <label htmlFor="email" className="mb-2 block text-xs text-white/80">
          Your Email Address:
        </label>
        <input
          id="email"
          type="email"
          placeholder="Enter Your Email Address*"
          className="w-full rounded-2xl bg-white/90 p-3 text-sm text-gray-800 placeholder-gray-500 focus:bg-white focus:outline-none focus:ring-2 focus:ring-[#FFA600]"
          {...register("email")}
        />
        {errors.email && (
          <p className="mt-1 text-xs text-red-300">{errors.email.message}</p>
        )}
      </div>

      {/* Phone Number Field */}
      <div>
        <label htmlFor="phone" className="mb-2 block text-xs text-white/80">
          Phone Number:
        </label>
        <input
          id="phone"
          type="tel"
          placeholder="Enter Your Phone Number"
          className="w-full rounded-2xl bg-white/90 p-3 text-sm text-gray-800 placeholder-gray-500 focus:bg-white focus:outline-none focus:ring-2 focus:ring-[#FFA600]"
          {...register("phone")}
        />
        {errors.phone && (
          <p className="mt-1 text-xs text-red-300">{errors.phone.message}</p>
        )}
      </div>

      {/* Choose Your Location Dropdown */}
      <div>
        <label htmlFor="location" className="mb-2 block text-xs text-white/80">
          Choose Your Location:
        </label>
        <select
          id="location"
          className="w-full rounded-2xl bg-white/90 p-3 text-sm text-gray-800 focus:bg-white focus:outline-none focus:ring-2 focus:ring-[#FFA600]"
          defaultValue=""
          {...register("location")}
        >
          <option value="" disabled>
            Select a Location
          </option>
          <option value="calgary">Calgary</option>
          <option value="cochrane">Cochrane</option>
          <option value="airdrie">Airdrie</option>
          <option value="red-deer">Red Deer</option>
          <option value="okotoks">Okotoks</option>
        </select>
        {errors.location && (
          <p className="mt-1 text-xs text-red-300">{errors.location.message}</p>
        )}
      </div>

      {/* Product of Interest Dropdown */}
      <div>
        <label htmlFor="product" className="mb-2 block text-xs text-white/80">
          Product of Interest:
        </label>
        <select
          id="product"
          className="w-full rounded-2xl bg-white/90 p-3 text-sm text-gray-800 focus:bg-white focus:outline-none focus:ring-2 focus:ring-[#FFA600]"
          defaultValue=""
          {...register("product")}
        >
          <option value="" disabled>
            Select a Product
          </option>
          <option value="roller-blinds">Roller Blinds</option>
          <option value="roman-shades">Roman Shades</option>
          <option value="honeycomb-blinds">Honeycomb Blinds</option>
          <option value="double-roller-blinds">Double Roller Blinds</option>
          <option value="drapery-blinds">Drapery Blinds</option>
          <option value="luxury-roman-shades">Luxury Roman Shades</option>
          <option value="dream-curtains">Dream Curtains</option>
          <option value="motorized-blinds">Motorized Blinds</option>
          <option value="other">Other</option>
        </select>
        {errors.product && (
          <p className="mt-1 text-xs text-red-300">{errors.product.message}</p>
        )}
      </div>

      {/* Type of Inquiry Dropdown */}
      <div>
        <label htmlFor="inquiry" className="mb-2 block text-xs text-white/80">
          Type of Inquiry:
        </label>
        <select
          id="inquiry"
          className="w-full rounded-2xl bg-white/90 p-3 text-sm text-gray-800 focus:bg-white focus:outline-none focus:ring-2 focus:ring-[#FFA600]"
          defaultValue=""
          {...register("inquiry")}
        >
          <option value="" disabled>
            Select an Inquiry Type
          </option>
          <option value="free-estimate">Free Estimate</option>
          <option value="product-information">Product Information</option>
          <option value="warranty">Warranty</option>
          <option value="repair-service">Repair Service</option>
          <option value="general-inquiry">General Inquiry</option>
          <option value="other">Other</option>
        </select>
        {errors.inquiry && (
          <p className="mt-1 text-xs text-red-300">{errors.inquiry.message}</p>
        )}
      </div>

      {/* Where Did You Hear About Us? Dropdown */}
      <div className="col-span-full">
        <label htmlFor="source" className="mb-2 block text-xs text-white/80">
          Where Did You Hear About Us?:
        </label>
        <select
          id="source"
          className="w-full rounded-2xl bg-white/90 p-3 text-sm text-gray-800 focus:bg-white focus:outline-none focus:ring-2 focus:ring-[#FFA600]"
          defaultValue=""
          {...register("source")}
        >
          <option value="" disabled>
            Select a Source
          </option>
          <option value="google-search">Google Search</option>
          <option value="social-media">Social Media</option>
          <option value="referral">Referral from Friend/Family</option>
          <option value="advertisement">Advertisement</option>
          <option value="home-show">Home Show</option>
          <option value="other">Other</option>
        </select>
        {errors.source && (
          <p className="mt-1 text-xs text-red-300">{errors.source.message}</p>
        )}
      </div>

      {/* Message Textarea */}
      <div className="col-span-full">
        <label htmlFor="message" className="mb-2 block text-xs text-white/80">
          Message:
        </label>
        <textarea
          id="message"
          placeholder="Enter Your Message*"
          rows={4}
          className="w-full resize-none rounded-2xl bg-white/90 p-3 text-sm text-gray-800 placeholder-gray-500 focus:bg-white focus:outline-none focus:ring-2 focus:ring-[#FFA600]"
          {...register("message")}
        />
        {errors.message && (
          <p className="mt-1 text-xs text-red-300">{errors.message.message}</p>
        )}
      </div>

      {/* Submit Button */}
      <div className="col-span-full">
        <button
          type="submit"
          disabled={isSubmitting || cooldown}
          className={`w-full rounded-2xl bg-[#FFA600] py-1 font-semibold text-[#013F68] transition-all duration-200 hover:bg-[#FF9500] ${
            isSubmitting || cooldown
              ? "cursor-not-allowed opacity-50"
              : "hover:shadow-lg"
          }`}
        >
          {isSubmitting ? "Sending..." : "Get Free Estimate"}
        </button>
      </div>
    </form>
  );
}
