import { useState, useRef, useEffect } from "react";
import { IoIosArrowDown } from "react-icons/io";

interface FAQsCardProps {
  question: string;
  answer: string;
}

const FAQsCard = ({ question, answer }: FAQsCardProps) => {
  const [isOpen, setIsOpen] = useState(false);
  const contentRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (contentRef.current) {
      contentRef.current.style.maxHeight = isOpen
        ? `${contentRef.current.scrollHeight}px`
        : "0px";
    }
  }, [isOpen]);
  return (
    <div
      className={`box-border w-full overflow-hidden rounded-xl border duration-400 ${isOpen ? "mb-4 border-[#FFB83D]" : "border-[#F7F9FA]"}`}
    >
      {/* Question header - always visible */}
      <div
        onClick={() => setIsOpen(!isOpen)}
        className={`grid grid-cols-[1fr_auto] items-center duration-400 ${
          isOpen ? "bg-[#FFB83D]" : "bg-[#F7F9FA]"
        } min-h-[56px] px-11 py-5 transition-colors duration-300 sm:min-h-[64px] md:min-h-[72px]`}
      >
        <h3
          className={`text-base font-bold duration-400 sm:text-lg md:text-xl ${isOpen ? "text-white" : "text-[#454545]"}`}
        >
          {question}
        </h3>
        <div
          className={`ml-4 flex h-8 w-8 items-center justify-center rounded-full border sm:h-10 sm:w-10 md:h-12 md:w-12 cursor-pointer ${
            isOpen ? "border-white" : "border-[#FFB83D]"
          }`}
        >
          <IoIosArrowDown
            className={`text-xl duration-400 sm:text-2xl md:text-3xl ${isOpen ? "rotate-180 text-white" : "text-[#FFB83D]"}`}
          />
        </div>
      </div>

      {/* Answer content with animation */}
      <div
        className={`overflow-hidden transition-[max-height,opacity] duration-300 ease-in-out ${
          isOpen ? "max-h-[500px] opacity-100" : "max-h-0 opacity-0"
        }`}
        ref={contentRef}
      >
        <div className="px-11 py-6">
          <div className="border-l-4 border-[#FFB83D] ps-3">
            <p className="text-base text-[#585858] sm:text-lg">{answer}</p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default FAQsCard;
