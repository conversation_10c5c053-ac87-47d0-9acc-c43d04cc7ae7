"use client";
import { rubik } from "@/fonts";
import ReviewCard from "../common/cards/ReviewCard";
import { Swiper, SwiperSlide } from "swiper/react";
import { Autoplay, Navigation, Pagination } from "swiper/modules";
import "swiper/css";
import "swiper/css/navigation";
import "swiper/css/pagination";
import { useRef } from "react";
import { CiCircleChevLeft, CiCircleChevRight } from "react-icons/ci";

type Review = {
  userImage: string;
  userName: string;
  rating: number;
  date: string;
  comment: string;
};
type Props = {
  data: {
    heading: string;
    reviews: Review[];
  };
};

const BlindsTestimonials = ({ data }: Props) => {
  const swiperRef = useRef<any>(null);

  const handlePrevSlide = () => {
    if (swiperRef.current) {
      swiperRef.current.slidePrev();
    }
  };

  const handleNextSlide = () => {
    if (swiperRef.current) {
      swiperRef.current.slideNext();
    }
  };

  return (
    <div className="pt-12 md:pt-16 lg:pt-24">
      <div className="mx-auto max-w-[1560px] px-4 sm:px-6 lg:px-8">
        <div className="flex flex-col items-center">
          <h2
            className={`${rubik.className} relative z-[1] mb-6 text-2xl font-semibold text-[#013F68] after:absolute after:left-[45%] after:top-0 after:-z-10 after:h-10 after:w-[100px] after:rounded-full after:bg-[#FFA600] sm:text-3xl sm:after:h-12 sm:after:w-[120px] md:mb-8 md:text-4xl md:after:h-14 md:after:w-[140px] lg:mb-10 lg:text-5xl`}
          >
            {data.heading}
          </h2>
        </div>
        <div className="relative flex items-center gap-2 sm:gap-3">
          <button
            className="text-4xl text-[#FFA600] transition-opacity hover:opacity-80 sm:text-5xl md:text-6xl"
            onClick={handlePrevSlide}
          >
            <CiCircleChevLeft />
          </button>
          <Swiper
            onSwiper={(swiper) => {
              swiperRef.current = swiper;
            }}
            modules={[Autoplay, Navigation, Pagination]}
            spaceBetween={16}
            slidesPerView={1}
            pagination={{
              clickable: true,
              dynamicBullets: true,
            }}
            autoplay={{
              delay: 3000,
              disableOnInteraction: false,
            }}
            breakpoints={{
              640: {
                slidesPerView: 1,
                spaceBetween: 20,
              },
              768: {
                slidesPerView: 2,
                spaceBetween: 24,
              },
              1024: {
                slidesPerView: 3,
                spaceBetween: 30,
              },
            }}
            className="h-full w-full"
          >
            {data.reviews.map((review, index) => (
              <SwiperSlide className="h-full" key={index}>
                {/* <ReviewCard
                  userImage={review.userImage}
                  userName={review.userName}
                  rating={review.rating}
                  date={review.date}
                  comment={review.comment}
                /> */}
              </SwiperSlide>
            ))}
          </Swiper>
          <button
            className="text-4xl text-[#FFA600] transition-opacity hover:opacity-80 sm:text-5xl md:text-6xl"
            onClick={handleNextSlide}
          >
            <CiCircleChevRight />
          </button>
        </div>
      </div>
    </div>
  );
};

export default BlindsTestimonials;
