"use client";
import React from "react";
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>a<PERSON><PERSON> } from "react-icons/fa";
import { useRouter } from "next/navigation";
// import { toast } from "react-toastify";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  useD<PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
} from "@heroui/react";

interface BlogCardProps {
  data: {
    id: string;
    title: string;
    date: string;
    featured_image: {
      url: string;
      alt_text: string;
    };
    author: string;
    post_status?: string;
  };
  blogs?: any[];
  setBlogs?: (blogs: any[]) => void;
  isAdminEdit?: boolean;
  isAdminDelete?: boolean;
}

const BlogCard: React.FC<BlogCardProps> = ({
  data,
  isAdminEdit,
  isAdminDelete,
}) => {
  const { title, date, featured_image, author } = data;
  const router = useRouter();

 

  return (
    <div
      style={{
        position: "relative",
        border: "1px solid #ccc",
        borderRadius: "8px",
        overflow: "hidden",
      }}
      className="w-full cursor-pointer"
      onClick={() => {
        router.push(`/blog?id=${data.id}`);
      }}
    >
      {featured_image?.url ? (
        <img
          src={featured_image.url}
          alt={featured_image.alt_text}
          className="aspect-video w-full object-cover"
          style={{ width: "100%", height: "auto" }}
        />
      // <></>
      ) : (
        <div className="aspect-video w-full bg-gray-200"></div>
      )}

      {/* <div style={{ padding: "16px" }}>
        <div className="mb-1 flex items-center justify-end gap-2">
          {isAdminEdit && data?.post_status && (
            <div className="text-sm text-gray-500">{data.post_status}</div>
          )}
     
        
        </div>
        <h2 style={{ margin: "0 0 4px 0" }}>{title}</h2>
        <p style={{ color: "#777", marginBottom: "4px" }}>{author}</p>
        <p style={{ color: "#777", margin: "0" }}>{date}</p>
      </div> */}
    </div>
  );
};

export default BlogCard;
