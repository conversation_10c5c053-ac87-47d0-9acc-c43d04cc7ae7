.glide {
  max-width: 1000px;
  margin: 0 auto;
}

.glide * {
  box-sizing: inherit;
}

.glide__track {
  overflow: hidden;
}

.glide__slides {
  position: relative;
  width: 100%;
  list-style: none;
  backface-visibility: hidden;
  transform-style: preserve-3d;
  touch-action: pan-Y;
  padding: 0;
  margin: 0;
  display: flex;
  flex-wrap: nowrap;
  will-change: transform;
}

.glide__slides--dragging {
  user-select: none;
}

.glide__slide {
  width: 100%;
  height: 100%;
  flex-shrink: 0;
  white-space: normal;
  user-select: none;
  -webkit-touch-callout: none;
  -webkit-tap-highlight-color: transparent;
}

.glide__slide a {
  user-select: none;
  -webkit-user-drag: none;
  -moz-user-select: none;
  -ms-user-select: none;
}

.glide__arrows {
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

.glide__arrow {
  position: absolute;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  z-index: 2;
}

.glide__arrow:hover {
  background-color: #f8f8f8;
}

.glide__arrow--left {
  left: 0;
}

.glide__arrow--right {
  right: 0;
}

.glide__arrow--disabled {
  opacity: 0.33;
  cursor: auto;
}

/* Bullets for pagination */
.glide__bullets {
  position: absolute;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  list-style: none;
  padding: 0;
  z-index: 2;
}

.glide__bullet {
  width: 10px;
  height: 10px;
  border-radius: 50%;
  background-color: rgba(255, 166, 0, 0.5);
  border: 0;
  margin: 0 4px;
  padding: 0;
  cursor: pointer;
  transition: all 0.3s ease;
}

.glide__bullet:hover,
.glide__bullet:focus {
  background-color: #ffa600;
  outline: none;
}

.glide__bullet--active {
  background-color: #ffa600;
}

/* Responsive adjustments for small screens */
@media (max-width: 767px) {
  .glide__bullets {
    bottom: 10px;
  }

  .glide__bullet {
    width: 12px;
    height: 12px;
    margin: 0 6px;
  }
}
