"use client";
import { websiteInfo } from "@/configs/info";
import type React from "react";

import { mainNavbarNavigation } from "@/configs/navigation";
import { Squash as Hamburger } from "hamburger-react";
import Link from "next/link";
import { usePathname, useRouter } from "next/navigation";
import { useState, useCallback, useEffect } from "react";
import {
  Drawer,
  DrawerContent,
  useDisclosure,
  Accordion,
  AccordionItem,
  Spinner,
} from "@heroui/react";
import useWarrantyDialog from "@/hooks/useWarrantyDialog";
import Image from "next/image";
import { useScrollPosition } from "@/hooks/useScroll";

const MainNavbar = () => {
  const logo = websiteInfo.logo;
  const pathname = usePathname();
  const { isOpen, onOpenChange } = useDisclosure();
  const router = useRouter();
  const { openDialog } = useWarrantyDialog();
  const [isNavigating, setIsNavigating] = useState(false);
  const [isNavigationInProgress, setIsNavigationInProgress] = useState(false);

  // Use our custom scroll hook with scrollToTop functionality
  const { scrolled, scrollToTop } = useScrollPosition();

  // Add this effect to stop the spinner after navigation or after timeout
  useEffect(() => {
    setIsNavigating(false);
    setIsNavigationInProgress(false);

    // Safety timeout to prevent infinite spinner
    const safetyTimeout = setTimeout(() => {
      setIsNavigating(false);
      setIsNavigationInProgress(false);
    }, 3000);

    return () => clearTimeout(safetyTimeout);
  }, [pathname]);

  // Optimized navigation handler with debounce
  const handleNavigation = useCallback(
    (path: string) => {
      if (isNavigationInProgress) return;

      // Don't navigate if we're already on this path
      if (pathname === path) {
        return;
      }

      setIsNavigationInProgress(true);
      setIsNavigating(true);
      router.push(path);
    },
    [router, isNavigationInProgress, pathname],
  );

  // Logo click handler - navigate home and scroll to top
  const handleLogoClick = (e: React.MouseEvent) => {
    // If already on homepage, just scroll to top
    if (pathname === "/") {
      e.preventDefault();
      scrollToTop();
    }
    // Otherwise Next.js Link will handle navigation to home
  };

  const ImageInnerNavigation = ({
    title,
    link,
    image,
    onClose,
  }: {
    title: string;
    link: string;
    image: string;
    onClose?: () => void;
  }) => {
    const [isLoading, setIsLoading] = useState(true);

    return (
      <Link
        className="group/link items-center space-y-3.5"
        href={link}
        onClick={onClose}
      >
        <div className="relative mx-auto aspect-square w-[79px]">
          {isLoading && (
            <div className="absolute inset-0 flex items-center justify-center">
              <Spinner size="sm" color="warning" />
            </div>
          )}
          <Image
            src={image || "/placeholder.svg"}
            alt={title}
            width={79}
            height={79}
            className={`duration-300 group-hover/link:[filter:invert(13%)_sepia(99%)_saturate(2083%)_hue-rotate(1deg)_brightness(102%)_contrast(101%)] ${pathname == link ? "[filter:invert(13%)_sepia(99%)_saturate(2083%)_hue-rotate(1deg)_brightness(102%)_contrast(101%)]" : "group-hover/link:[filter:invert(13%)_sepia(99%)_saturate(2083%)_hue-rotate(1deg)_brightness(102%)_contrast(101%)]"}`}
            onLoadingComplete={() => setIsLoading(false)}
          />
        </div>
        <div className="text-center text-xs text-black">{title}</div>
      </Link>
    );
  };

  return (
    <>
      {/* Mobile: Only the floating hamburger - always visible */}
      <div className="fixed right-4 top-[50px] z-50 rounded-full bg-white p-1 shadow-md xl:hidden">
        <Hamburger
          toggled={isOpen}
          toggle={onOpenChange}
          size={16}
          color="#013F68"
        />
      </div>

      {/* Loading indicator */}
      {/* {isNavigating && (
        <div className="fixed inset-0 z-[100] flex items-center justify-center bg-black/20">
          <Spinner size="lg" color="warning" />
        </div>
      )} */}

      {/* Mobile header content - hidden on scroll down - zero transition duration */}
      <div className={`left-0 top-11 z-40 w-full bg-white xl:hidden`}>
        <div className="px-3 py-3">
          <div className="flex items-center justify-between">
            {/* Left: Logo */}
            <div className="flex items-center">
              <Link href="/" onClick={handleLogoClick} prefetch>
                <img
                  className="ml-2 pt-2"
                  width={200}
                  height={50}
                  src={logo.src || "/placeholder.svg"}
                  alt={logo.alt}
                />
              </Link>
            </div>
          </div>
        </div>
      </div>

      {/* Desktop: Full navigation - only visible on xl screens */}
      <div className="hidden xl:block">
        <div className="global-container">
          <div className="flex items-center justify-between px-3">
            {/* Left: Logo */}
            <div className="flex items-center">
              <Link href="/" onClick={handleLogoClick} prefetch>
                <img
                  width={200}
                  height={50}
                  src={logo.src || "/placeholder.svg"}
                  alt={logo.alt}
                />
              </Link>
            </div>

            {/* Navigation for desktop */}
            <div className="flex items-center gap-10">
              {mainNavbarNavigation.map((navigation, index) => {
                if (navigation.isButton && navigation.link) {
                  return (
                    <Link
                      key={index}
                      href={navigation.link}
                      prefetch
                      onClick={(e) => {
                        if (pathname === navigation.link) {
                          e.preventDefault();
                          return;
                        }
                        setIsNavigating(true);
                      }}
                      className={`my-8 flex items-center gap-1 rounded-full bg-[#FFA600] px-5 py-2 text-white`}
                    >
                      <navigation.icon className="h-5 w-5" />
                      <span> {navigation.title}</span>
                    </Link>
                  );
                } else if (
                  navigation.isButton &&
                  navigation.title === "Warranty"
                ) {
                  return (
                    <button
                      key={index}
                      className={`flex items-center gap-1 py-10 text-sm text-[#013F68] duration-150 hover:text-[#FFA600]`}
                      onClick={openDialog}
                    >
                      <navigation.icon className="h-5 w-5" />
                      <span>{navigation.title}</span>
                    </button>
                  );
                } else if (
                  navigation.link &&
                  !navigation.isButton &&
                  !navigation.items
                ) {
                  return (
                    <Link
                      key={index}
                      prefetch
                      onClick={(e) => {
                        if (pathname === navigation.link) {
                          e.preventDefault();
                          return;
                        }
                        setIsNavigating(true);
                      }}
                      className={`flex items-center gap-1 py-10 text-sm duration-150 ${pathname == navigation.link ? "text-[#FFA600]" : "text-[#013F68] hover:text-[#FFA600]"}`}
                      href={navigation.link}
                    >
                      <navigation.icon className="h-5 w-5" />
                      <span> {navigation.title}</span>
                    </Link>
                  );
                } else if (navigation.items) {
                  return (
                    <div key={index} className="group relative inline-block">
                      <button
                        className="flex items-center gap-1 py-10 text-sm text-[#013F68] duration-150 hover:text-[#FFA600]"
                        onClick={() => {
                          if (navigation.link) {
                            handleNavigation(navigation.link);
                          }
                        }}
                      >
                        <navigation.icon className="h-5 w-5" />
                        <span> {navigation.title}</span>
                      </button>
                      <div
                        className={`absolute top-[calc(100%+20px)] z-50 grid -translate-x-1/2 cursor-default overflow-auto rounded-lg bg-white px-10 py-9 opacity-0 duration-300 [column-gap:50px] [row-gap:19px] [visibility:hidden] group-hover:visible group-hover:top-full group-hover:opacity-100`}
                        style={{
                          gridTemplateColumns: navigation?.itemsCount
                            ? `repeat(${navigation?.itemsCount}, 1fr)`
                            : "repeat(2, 1fr)",
                          minWidth: navigation?.itemsContainerWidth
                            ? `${navigation?.itemsContainerWidth}px`
                            : "300px",
                        }}
                      >
                        {navigation.items.map((item, index) => (
                          <ImageInnerNavigation
                            key={index}
                            title={item.title}
                            link={item.link}
                            image={item.image}
                          />
                        ))}
                      </div>
                    </div>
                  );
                }
              })}
            </div>
          </div>
        </div>
      </div>

      <Drawer
        isOpen={isOpen}
        size="lg"
        className="rounded-none xl:hidden"
        onOpenChange={onOpenChange}
      >
        <DrawerContent className="rounded-none">
          {(onClose) => (
            <nav className="flex-col items-start p-4 xl:flex">
              {mainNavbarNavigation.map((navigation, index) => {
                if (navigation.isButton && navigation.title === "Warranty") {
                  return (
                    <div key={index}>
                      <button
                        onClick={() => {
                          openDialog();
                          onClose();
                        }}
                        className={`flex items-center gap-3 rounded-full px-5 py-6 text-lg text-[#013F68] hover:text-[#FFA600]`}
                      >
                        <navigation.icon className="h-5 w-5" />
                        <span>{navigation.title}</span>
                      </button>
                    </div>
                  );
                } else if (navigation.items) {
                  return (
                    <div key={index}>
                      <Accordion className="px-0">
                        <AccordionItem
                          className="px-0"
                          HeadingComponent={(item) => (
                            <div className="m-0 flex items-center justify-between p-0">
                              <button
                                className={`flex items-center gap-3 whitespace-nowrap rounded-full px-5 py-6 text-lg text-[#013F68] ${pathname == navigation.link ? "text-[#FFA600]" : "hover:text-[#FFA600]"}`}
                                onClick={(e) => {
                                  if (isNavigationInProgress) {
                                    e.preventDefault();
                                    return;
                                  }
                                  setIsNavigating(true);
                                  setIsNavigationInProgress(true);
                                  if (typeof onClose === "function") onClose();
                                }}
                              >
                                <navigation.icon className="h-5 w-5" />
                                {navigation.title}
                              </button>{" "}
                              {item.children}
                            </div>
                          )}
                        >
                          <div className="grid w-full grid-cols-3 gap-5">
                            {navigation.items.map((item, index) => (
                              <ImageInnerNavigation
                                key={index}
                                onClose={onClose}
                                {...item}
                              />
                            ))}
                          </div>
                        </AccordionItem>
                      </Accordion>
                    </div>
                  );
                } else {
                  return (
                    <div key={index}>
                      <Link
                        href={navigation?.link || ""}
                        prefetch
                        onClick={(e) => {
                          if (isNavigationInProgress) {
                            e.preventDefault();
                            return;
                          }
                          if (pathname === navigation?.link) {
                            e.preventDefault();
                            if (typeof onClose === "function") onClose();
                            return;
                          }
                          setIsNavigating(true);
                          setIsNavigationInProgress(true);
                          if (typeof onClose === "function") onClose();
                        }}
                        className={`flex items-center gap-3 rounded-full px-5 py-6 text-lg text-[#013F68] ${pathname == navigation.link ? "text-[#FFA600]" : "hover:text-[#FFA600]"}`}
                      >
                        <navigation.icon className="h-5 w-5" />
                        <span> {navigation.title}</span>
                      </Link>
                    </div>
                  );
                }
              })}
            </nav>
          )}
        </DrawerContent>
      </Drawer>
    </>
  );
};

export default MainNavbar;
