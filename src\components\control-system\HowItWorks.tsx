"use client";

import React, { useEffect, useState } from "react";
import Image from "next/image";
import { inter } from "@/fonts";
import {
  Carousel,
  CarouselApi,
  CarouselContent,
  CarouselItem,
} from "../ui/carousel";

interface Step {
  icon: React.ReactNode;
  title: string;
  description: string;
  image: string;
  isFilled?: boolean;
}

interface HowItWorksData {
  heading: string;
  subHeading: string;
  steps: Step[];
}

type Props = {
  data: HowItWorksData;
};

const StepItem: React.FC<{
  data: Step & { isActive: boolean; onClick: () => void };
  isFirst: boolean;
  isLast: boolean;
}> = ({ data, isFirst, isLast }) => {
  return (
    <div
      className={`flex cursor-pointer items-center gap-3 rounded-xl px-3 py-2 transition duration-300 sm:gap-5 sm:px-5 sm:py-3`}
      onClick={data.onClick}
    >
      <div className="relative">
        {isFirst && (
          <div className="absolute -top-[20px] left-1/2 h-[20px] w-[6px] -translate-x-1/2 rounded-t-full bg-[#FFA600] sm:-top-[25px] sm:h-[25px] sm:w-[8px]" />
        )}
        <div className="h-[70px] w-[70px] rounded-full bg-[#F7F9FA] sm:h-[98px] sm:w-[98px]" />
        <div
          className={`border-6 absolute inset-[6px] flex items-center justify-center rounded-full border-[#FFA600] sm:inset-[10px] sm:border-8 ${
            data.isActive ? "bg-[#FFA600]" : "bg-white"
          }`}
        >
          <div
            className={`text-xl sm:text-2xl md:text-3xl ${
              data.isActive ? "text-white" : "text-[#FFA600]"
            }`}
          >
            {data.icon}
          </div>
        </div>
        {!isLast && (
          <div className="absolute -bottom-[40px] left-1/2 h-[40px] w-[6px] -translate-x-1/2 bg-[#FFA600] sm:-bottom-[52px] sm:h-[52px] sm:w-[8px]" />
        )}
        {isLast && (
          <div className="absolute -bottom-[20px] left-1/2 h-[20px] w-[6px] -translate-x-1/2 rounded-b-full bg-[#FFA600] sm:-bottom-[25px] sm:h-[25px] sm:w-[8px]" />
        )}
      </div>
      <div
        className={`text-base leading-6 sm:text-lg sm:leading-7 md:text-xl lg:text-2xl ${
          data.isActive ? "text-gray-500" : "text-gray-950"
        }`}
      >
        <h3
          className={`inline-block text-lg font-bold sm:text-xl md:text-2xl lg:text-3xl ${
            data.isActive ? "text-gray-700" : "text-gray-950"
          }`}
        >
          {data.title}
        </h3>{" "}
        - {data.description}
      </div>
    </div>
  );
};

const HowItWorks = ({ data }: Props) => {
  const [api, setApi] = useState<CarouselApi>();
  const [activeIndex, setActiveIndex] = useState(0);

  useEffect(() => {
    if (!api) return;

    const handleSelect = () => {
      setActiveIndex(api.selectedScrollSnap());
    };

    api.on("select", handleSelect);
    setActiveIndex(api.selectedScrollSnap());

    return () => {
      api.off("select", handleSelect);
    };
  }, [api]);
  useEffect(() => {
    if (!api) return;

    const interval = setInterval(() => {
      api.scrollNext();
    }, 3000);

    return () => {
      clearInterval(interval); // Clean up interval on unmount
    };
  }, [api]);

  const handleFeatureClick = (index: number) => {
    setActiveIndex(index);
    if (api) api.scrollTo(index);
  };

  return (
    <div className="w-full py-8 sm:py-12 md:py-16">
      <div className="mx-auto max-w-[1560px] px-4">
        <div className="mx-auto mb-8 flex max-w-[1200px] flex-col items-center px-3 sm:mb-14 md:mb-20">
          <h2
            className={`${inter.className} relative mb-6 text-center text-2xl font-bold text-[#013F68] after:absolute after:left-[45%] after:top-0 after:-z-10 after:h-10 after:w-[100px] after:rounded-full after:bg-[#FFA600] sm:mb-10 sm:text-3xl sm:after:h-14 sm:after:w-[140px] md:text-4xl lg:text-5xl`}
          >
            {data.heading}
          </h2>
          <p className="max-w-[1300px] text-center text-sm font-medium text-[#767676] sm:text-base md:text-xl lg:text-2xl">
            {data.subHeading}
          </p>
        </div>

        <div className="flex flex-col items-center justify-between gap-6 px-4 sm:gap-8 sm:px-6 md:gap-10 md:px-8 lg:flex-row">
          {/* Left: Steps List */}
          <div className="w-full lg:w-1/2">
            <div className="flex flex-col items-stretch gap-1 sm:gap-2">
              {data.steps?.map((step, index) => (
                <StepItem
                  key={index}
                  data={{
                    ...step,
                    isActive: activeIndex === index,
                    onClick: () => handleFeatureClick(index),
                  }}
                  isFirst={index === 0}
                  isLast={index === data.steps.length - 1}
                />
              ))}
            </div>
          </div>

          {/* Right: Image Carousel */}
          <Carousel
            setApi={setApi}
            opts={{ loop: true }}
            className="w-full lg:w-1/2"
          >
            <CarouselContent>
              {data.steps?.map((step, index) => (
                <CarouselItem key={index}>
                  <div className="relative h-full">
                    <div className="relative aspect-[16/10] max-h-[600px] w-full overflow-hidden rounded-[24px] sm:aspect-[16/9] sm:rounded-[32px] md:rounded-[42px]">
                      <Image
                        src={step.image}
                        alt={step.title}
                        fill
                        quality={85}
                        className="object-cover"
                        sizes="(max-width: 1024px) 100vw, 50vw"
                      />
                    </div>

                    {/* Dots */}
                    <div className="absolute bottom-3 left-1/2 flex -translate-x-1/2 gap-1.5 sm:bottom-4 sm:gap-2">
                      {data.steps?.map((_, i) => (
                        <button
                          key={i}
                          onClick={() => handleFeatureClick(i)}
                          className={`h-2 w-2 rounded-full transition-all sm:h-3 sm:w-3 ${
                            activeIndex === i
                              ? "w-4 bg-gray-500 sm:w-6"
                              : "bg-gray-300"
                          }`}
                        />
                      ))}
                    </div>
                  </div>
                </CarouselItem>
              ))}
            </CarouselContent>
          </Carousel>
        </div>
      </div>
    </div>
  );
};

export default HowItWorks;
