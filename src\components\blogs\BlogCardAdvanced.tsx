import { a<PERSON><PERSON>, inter, rubik } from "@/fonts";
import React from "react";
import { useRouter } from "next/navigation";

interface BlogCardAdvancedProps {
  textDirection?: "left" | "right";
  data: {
    id: number;
    documentId: string;
    title: string;
    description: string;
    slug: string;
    createdAt: string;
    updatedAt: string;
    publishedAt: string;
    author: string;
    cover: {
      id: number;
      url: string;
      alternativeText: string | null;
      formats: {
        thumbnail: { url: string };
        small?: { url: string };
        medium?: { url: string };
        large?: { url: string };
      };
    };
  };
}

const BlogCardAdvanced: React.FC<BlogCardAdvancedProps> = ({
  textDirection = "left",
  data,
}) => {
  const { id, title, description, publishedAt, author, slug, cover } = data;
  const router = useRouter();
  const formattedDate = new Date(publishedAt).toLocaleDateString("en-US", {
    month: "long",
    day: "numeric",
    year: "numeric",
  });

  // Get the best available image format
  const getImageUrl = () => {
    if (cover?.formats?.large?.url) return cover.formats.large.url;
    if (cover?.formats?.medium?.url) return cover.formats.medium.url;
    if (cover?.formats?.small?.url) return cover.formats.small.url;
    if (cover?.url) return cover.url;
    return null;
  };

  const imageUrl = getImageUrl();
  const baseUrl =
    `${process.env.NEXT_PUBLIC_API_URL}/api` || "http://localhost:1337";
  const anotherUrl = `${process.env.NEXT_PUBLIC_API_URL}`;

  return (
    <div
      className="w-full cursor-pointer overflow-hidden rounded-[42px] bg-[#FAFAFA]"
      onClick={() => router.push(`/blog/${slug}`)}
    >
      <div className="relative flex flex-col items-center lg:flex-row">
        {/* Image Section */}
        <div
          className={`relative h-full max-h-[600px] min-h-48 w-full sm:min-h-56 md:min-h-96 lg:flex-1 ${textDirection === "left" ? "order-1 lg:order-2" : "order-1 lg:order-1"}`}
        >
          {imageUrl ? (
            <img
              src={`${anotherUrl}${imageUrl}`}
              alt={cover?.alternativeText || title}
              className="absolute left-0 top-0 h-full w-full object-cover"
            />
          ) : (
            <div className="absolute left-0 top-0 h-full w-full bg-gray-200"></div>
          )}
        </div>
        {/* Content Section */}
        <div
          className={`w-full bg-[#F7F9FA] p-8 lg:w-1/2 lg:max-w-[600px] lg:p-12 xl:max-w-[700px] ${textDirection === "left" ? "order-2 lg:order-1" : "order-2 lg:order-2"}`}
        >
          <div className="flex h-full flex-col">
            {/* Author */}
            <p className="mb-6 text-sm font-semibold text-[#6E6E73]">
              {author}
            </p>

            {/* Title and description */}
            <div className="flex-grow">
              <h2
                className={`${inter.className} mb-8 text-2xl font-semibold lg:text-3xl`}
              >
                {title}
              </h2>
              <p
                className={`${rubik.className} mb-8 text-base text-[#999999] lg:text-lg`}
              >
                {description}
              </p>
              {/* Footer */}
              <div className="flex items-center justify-between text-sm text-[#6E6E73]">
                <span className="font-bold">{formattedDate}</span>
                <button
                  onClick={() => router.push(`/blog/${slug}`)}
                  className={`${abeezee.className} cursor-pointer transition-colors hover:text-black`}
                >
                  Read More
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default BlogCardAdvanced;
