"use client";
import React from "react";
import { inter, roboto, rubik } from "@/fonts";
import { Carousel, CarouselContent, CarouselItem } from "../ui/carousel";
import { useCarouselControl } from "@/hooks/useCarousel";
import { scrollToTop } from "@/hooks/useScroll";
import Image from "next/image";

interface Feature {
  icon: React.ReactNode;
  title: string;
  description?: string;
  image: string;
}

type Props = {
  data: {
    heading: string;
    features: Feature[];
  };
};

const WhyChooseBlindsSlider: React.FC<Props> = ({ data }) => {
  const { api, setApi, activeIndex, handleItemClick } =
    useCarouselControl(15000);

  // Enhanced feature click handler that combines slider navigation with scrollToTop
  const handleFeatureClick = (index: number) => {
    // If we're already on this slide, just scroll to top
    if (activeIndex === index) {
      scrollToTop();
    } else {
      // Otherwise, navigate to the slide
      handleItemClick(index);
    }
  };

  return (
    <div className="w-full py-8 sm:py-12 md:py-16">
      <div className="mx-auto max-w-[1586px] px-4 sm:px-6 lg:px-8">
        <div className="flex flex-col items-center justify-between gap-6 px-4 sm:gap-8 lg:flex-row lg:gap-8 xl:gap-16 2xl:gap-28">
          {/* Left Content */}
          <div className="w-full lg:w-1/2">
            <div className="mb-4">
              <h2
                className={`${roboto.className} relative z-0 mb-6 text-2xl font-semibold text-[#013F68] after:absolute after:left-[45%] after:top-0 after:-z-10 after:h-10 after:w-[100px] after:rounded-full after:bg-[#FFA600] sm:mb-8 sm:text-3xl sm:after:h-12 sm:after:w-[120px] md:mb-10 md:text-4xl md:after:h-14 md:after:w-[140px] lg:text-5xl`}
              >
                {data.heading}
              </h2>
            </div>

            {/* Features Rendering Section */}
            <div className="flex flex-col items-stretch gap-2 sm:gap-3">
              {data.features.map((feature, index) => {
                const isActive = activeIndex === index;
                return (
                  <div
                    key={index}
                    className={`flex cursor-pointer items-center gap-3 rounded-xl px-3 py-2 transition duration-300 sm:gap-4 sm:px-4 sm:py-2.5 md:gap-5 md:px-5 md:py-3 ${
                      isActive
                        ? "scale-[1.02] bg-[#FFAD33] text-white sm:scale-[1.03] md:scale-105"
                        : ""
                    }`}
                    onClick={() => handleFeatureClick(index)}
                  >
                    <div className="">
                      <div
                        className={`h-full w-full text-3xl sm:text-4xl md:text-5xl lg:text-6xl ${
                          isActive ? "text-white" : "text-gray-500"
                        }`}
                      >
                        {feature.icon}
                      </div>
                    </div>
                    <div>
                      <h3
                        className={`text-sm font-semibold sm:text-base md:text-lg lg:text-xl ${rubik.className} ${
                          isActive ? "text-white" : "text-gray-600"
                        }`}
                      >
                        {feature.title}
                      </h3>
                      {feature.description && (
                        <p
                          className={`mt-0.5 text-xs sm:mt-1 sm:text-sm md:text-base ${isActive ? "text-white" : "text-gray-600"} ${inter.className}`}
                        >
                          {feature.description}
                        </p>
                      )}
                    </div>
                  </div>
                );
              })}
            </div>
          </div>

          {/* Right Content - Slider */}
          <Carousel className="mt-6 w-full lg:mt-0 lg:w-1/2" setApi={setApi}>
            <CarouselContent>
              {data.features.map((feature, index) => (
                // <CarouselItem key={index}>
                //   <div className="relative h-full">
                //     <img
                //       width={1000}
                //       height={1000}
                //       src={feature.image}
                //       alt={feature.title}
                //       className="h-full max-h-[300px] w-full rounded-[20px] object-cover sm:max-h-[400px] sm:rounded-[30px] md:max-h-[500px] md:rounded-[42px] lg:max-h-[600px]"
                //     />
                //     <div className="absolute bottom-3 left-1/2 flex -translate-x-1/2 gap-1.5 sm:bottom-4 sm:gap-2">
                //       {data.features.map((_, i) => (
                //         <button
                //           key={i}
                //           onClick={() => handleFeatureClick(i)}
                //           className={`h-2 w-2 rounded-full transition-all sm:h-3 sm:w-3 ${
                //             activeIndex === i
                //               ? "w-4 bg-gray-500 sm:w-6"
                //               : "bg-gray-300"
                //           }`}
                //         ></button>
                //       ))}
                //     </div>
                //   </div>
                // </CarouselItem>

                <CarouselItem key={index}>
                  <div className="relative h-full">
                    <Image
                      src={feature.image}
                      alt={feature.title}
                      width={1000}
                      height={600}
                      className="h-full max-h-[300px] w-full rounded-[20px] object-cover sm:max-h-[400px] sm:rounded-[30px] md:max-h-[500px] md:rounded-[42px] lg:max-h-[600px]"
                      quality={90}
                      priority={index === 0}
                    />

                    {/* Dots Indicator */}
                    <div className="absolute bottom-3 left-1/2 flex -translate-x-1/2 gap-1.5 sm:bottom-4 sm:gap-2">
                      {data.features.map((_, i) => (
                        <button
                          key={i}
                          onClick={() => handleFeatureClick(i)}
                          className={`h-2 w-2 rounded-full transition-all sm:h-3 sm:w-3 ${
                            activeIndex === i
                              ? "w-4 bg-gray-500 sm:w-6"
                              : "bg-gray-300"
                          }`}
                        ></button>
                      ))}
                    </div>
                  </div>
                </CarouselItem>
              ))}
            </CarouselContent>
          </Carousel>
        </div>
      </div>
    </div>
  );
};

export default WhyChooseBlindsSlider;
