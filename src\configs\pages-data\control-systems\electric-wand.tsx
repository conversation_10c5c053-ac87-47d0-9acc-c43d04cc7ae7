// importing icons
import { FaReg<PERSON>lock } from "react-icons/fa6";
import { TbDeviceRemote } from "react-icons/tb";
import { IoShieldCheckmarkOutline } from "react-icons/io5";
import { IoAnalytics } from "react-icons/io5";
import { PiVolleyballLight } from "react-icons/pi";
import { FaShieldAlt } from "react-icons/fa";
import { FaRegCircleCheck } from "react-icons/fa6";
import { FiSun } from "react-icons/fi";
import { CiWallet } from "react-icons/ci";
import { FaLock, FaSun, FaArrowUp } from "react-icons/fa";
import { ControlSystemData } from "@/types/control-system";

const electricWand: ControlSystemData = {
  slug: "electric-wand",
  title: "Electric Wand",
  icon: "/icons/control-systems/electric-wand.svg",
  description:
    "Electric wand control makes operating your blinds effortless with motorized technology built into a convenient handheld wand.",
  image: "/images/controlsystem/electricwand/1.webp",
  /**
   * control system banner
   */
  controlSystemBanner: {
    heading: "Discover Electric Wand",
    subHeading:
      "From a traditional corded mechanism to cutting-edge Smart Home integrations, we offer a wide range of control systems, tailored to your interior, style, and preferences. Pick your best fit!",
    buttonText: "Request free Quote",
    coverImage: "/images/controlsystem/electricwand/9.webp",
  },
  /**
   * why choose control slider data
   */
  whyChooseControlSlider: {
    heading: "Why Electric Wands Might Be Perfect for You?",
    subHeading:
      "The simplicity of the wand system can be modernized with a motorized operation. Electric Wand A treatment, perfect for overhead windows, transforms any space into a mesmerizing one, while offering effortless control and reliability.",
    features: [
      {
        icon: <FaRegClock />,
        title: "Strong and durable manual control",
        image: "/images/controlsystem/electricwand/1.webp",
      },
      {
        icon: <FiSun />,
        title: "Precise light and privacy control",
        image: "/images/controlsystem/electricwand/2.webp",
      },
      {
        icon: <FaShieldAlt />,
        title: "Child safe lock mechanism prevents hazards",
        image: "/images/controlsystem/electricwand/3.webp",
      },
      {
        icon: <CiWallet />,
        title: "Budget friendly and easy to maintain",
        image: "/images/controlsystem/electricwand/4.webp",
      },
      {
        icon: <FaRegCircleCheck />,
        title: "Available in PVC, fabric, and stainless steel cord options",
        image: "/images/controlsystem/electricwand/5.webp",
      },
    ],
  },
  /**
   * production steps data
   */
  productionSteps: {
    heading: "Key Features of Electric Wand",
    subHeading:
      "A perfect infusion of style, convenience, and safety Electric Window Blinds come with a hassle free manual mechanism without sacrificing control over light and privacy. Their low maintenance design and state of the art safety features make them an ideal pick for parents and pet owners.",
    features: [
      {
        icon: FaRegClock,
        title: "Long lasting performance",
        description: "Equipped with highest quality mechanism that will last.",
      },
      {
        icon: TbDeviceRemote,
        title: "Wireless arrangement",
        description: "Sleek and minimalist look without hanging cords.",
      },
      {
        icon: IoShieldCheckmarkOutline,
        title: "Fully customizable",
        description: "Supports different cord sizes and materials.",
      },
      {
        icon: IoAnalytics,
        title: "Eye-catching windows",
        description:
          "Contemporary and stylish system that will enhance any space.",
      },
      {
        icon: PiVolleyballLight,
        title: "Enhanced safety",
        description: "Added layer of protection for children and pets.",
      },
    ],
  },
  /**
   * compatible blinds data
   */
  compatibleBlinds: {
    heading: "Compatible Blinds",
    subHeading:
      "Cordless control is compatible with multiple blind types, offering a modern and safe solution.",
    blinds: [
      {
        title: "Zebra Blinds",
        description:
          "This style of blinds lets you control the light with blackout or light filtering fabric perfect for full darkness or natural brightness. A great custom option for any home in Canada.",
        image: "/images/blinds/zebra-blind.webp",
        buttonText: "Learn More",
        slug: "zebra-blinds",
        type: "blinds",
      },
      {
        title: "Roller Shades",
        description:
          "These clean, minimalist roller shades offer privacy and style without clutter. Easy to use and clean, they roll up or down to control light in any space.",
        image: "/images/blinds/roller-shades-blind.webp",
        buttonText: "Learn More",
        slug: "roller-shades",
        type: "blinds",
      },
      {
        title: "Solar Shades",
        description:
          "Solar blinds reduce glare, block harmful UV rays, and help keep your space cool and comfortable. They're a top choice for energy-efficient window shades.",
        image: "/images/blinds/solar-shades-blind.webp",
        buttonText: "Learn More",
        slug: "solar-shades",
        type: "blinds",
      },
      {
        title: "Drapery",
        description:
          "Add color and personality to your home with custom drapery. Choose from rich fabrics like velvet, silk, and linen perfect for both modern and traditional styles. Durable and stylish, our drapes resist dust, sun, and drafts.",
        image: "/images/blinds/drapery-blind.webp",
        buttonText: "Learn More",
        slug: "drapery",
        type: "blinds",
      },
      {
        title: "Trifold",
        description:
          "Save space with trifold shutters, featuring three panels that fold in half. Ideal for large windows, they offer a unique design and come in materials like wood or composite to match your style.",
        image: "/images/blinds/TrifoldBlinds/triflod-blind.webp",
        buttonText: "Learn More",
        slug: "trifold-blinds",
        type: "blinds",
      },
      {
        title: "Honeycomb Blinds",
        description:
          "A sleek and modern look at a budget-friendly price. Its versatile, moisture-resistant design makes it perfect for any room.",
        image: "/images/blinds/honey-comb-blind.webp",
        buttonText: "Learn More",
        slug: "honeycomb-blinds",
        type: "blinds",
      },
    ],
  },
  /**
   * how it works data
   */
  howItWorks: {
    heading: "Operating Mechanism",
    subHeading: "3-Step Guide to operate Electric Wand Control Blinds",
    steps: [
      {
        icon: <FaArrowUp />,
        title: "Switch On/Off",
        description: "Push the button to turn on/off the Electric wand.",
        isFilled: true,
        image: "/images/controlsystem/electricwand/6.webp",
      },
      {
        icon: <FaLock />,
        title: " Adjust",
        description:
          "Raise, tilt, or lower the blinds using the electric wand.",
        isFilled: false,
        image: "/images/controlsystem/electricwand/7.webp",
      },
      {
        icon: <FaSun />,
        title: "Control Light & Privacy",
        description: "Stop at any height for preferred privacy & lighting.",
        isFilled: false,
        image: "/images/controlsystem/electricwand/8.webp",
      },
    ],
  },
  /**
   * safety & maintenance data
   */
  safetyMaintenance: {
    heading: "Blinds Safety & Care",
    subHeading:
      "Routine cleaning and maintenance prevents dust build up, wear & tear and allergens, ensuring Electric Blind Wand's smooth operation and longevity. Regular check ups are essential to maintain safety and reliability of the system.",
    features: [
      {
        title: "Motor Maintenance",
        description: "Ensure that the motor is free from dust and debris.",
        image: "/svgs/Electric Wand/Motor Maintenance-14.svg",
      },
      {
        title: "Wand Inspection",
        description:
          "Check the wand connections and movement for uninterrupted operation.",
        image: "/svgs/Electric Wand/Wand Inspection-05.svg",
      },
      {
        title: "Power Check",
        description: "Regularly inspect power source and wiring.",
        image: "/svgs/Electric Wand/Power Check-12.svg",
      },
    ],
  },
  /**
   * quote cta section
   */
  quoteCTASection: {
    heading: "Want Us To Help You Choose?",
    subHeading:
      "Need expert advice for your home or office? Reach out to use and our team will provide you with personalized recommendations tailored to your preference and style.",
    buttonText: "Request free Quote",
    background: "/banners/about-banner-img.webp",
  },
};

export default electricWand;
