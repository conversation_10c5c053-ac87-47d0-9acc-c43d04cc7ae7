// import React from "react";
// import Image from "next/image";
// import { IoStar } from "react-icons/io5";

// interface ReviewCardProps {
//   userImage?: string;
//   userName: string;
//   rating?: number;
//   date: string | Date;
//   comment: string;
//   className?: string;
//   fromGoogle?: boolean;
// }

// const ReviewCard: React.FC<ReviewCardProps> = ({
//   userImage,
//   rating,
//   date,
//   comment,
//   userName,
//   className,
//   fromGoogle = false,
// }) => {
//   const defaultImage = "/profile-image.webp";
//   const displayImage = userImage || defaultImage;
//   const displayRating = rating ?? 5;

//   const receivedDate = new Date(date);
//   const formattedDate = isNaN(receivedDate.getTime())
//     ? "Invalid Date"
//     : receivedDate.toLocaleDateString("en-US", {
//         month: "2-digit",
//         day: "2-digit",
//         year: "numeric",
//       });

//   const stars = Array.from({ length: 5 }, (_, index) => (
//     <span
//       key={index}
//       className={
//         index < displayRating
//           ? "text-3xl text-yellow-500"
//           : "text-3xl text-gray-300"
//       }
//     >
//       <IoStar />
//     </span>
//   ));

//   return (
//     <div
//       className={`flex h-full flex-col items-center justify-center gap-4 rounded-lg border bg-white px-5 py-3.5 sm:px-10 sm:py-7 ${className}`}
//     >
//       {/* ✅ User Image */}
//       <div className="relative h-28 w-28 overflow-hidden rounded-full">
//         <Image
//           src={displayImage}
//           alt="User"
//           fill
//           className="object-cover"
//           sizes="112px"
//           quality={85}
//         />
//       </div>

//       <h5 className="text-center text-xl">{userName}</h5>
//       <p className="text-center text-gray-500">{formattedDate}</p>

//       {/* Rating Stars */}
//       <div className="flex justify-center space-x-1">{stars}</div>

//       {/* Comment */}
//       <p className="w-full flex-grow text-center text-lg">
//         {comment.length > 100 ? comment.slice(0, 100) + "..." : comment}
//       </p>

//       {/* ✅ Google Badge */}
//       {fromGoogle && (
//         <div className="relative h-7 w-7">
//           <Image
//             src="/G-icon.svg"
//             alt="Google Icon"
//             fill
//             className="object-contain"
//             sizes="28px"
//             quality={100}
//           />
//         </div>
//       )}
//     </div>
//   );
// };

// export default ReviewCard;

import React from "react";

function ReviewCard() {
  return <div></div>;
}

export default ReviewCard;
