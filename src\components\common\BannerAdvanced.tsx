"use client";
import React from "react";
import BannerI<PERSON> from "./BannerItem";
import BannerItemSkeleton from "./BannerItemSkeleton";
import { useGetBannersQuery } from "@/store/services/bannersApi";
import { FaChevronLeft, FaChevronRight } from "react-icons/fa";
import { useGlideSlider } from "@/hooks/useCarousel";
import { scrollToTop } from "@/hooks/useScroll";

// Define types for banner data
interface BannerImage {
  id: number;
  url: string;
  formats: {
    thumbnail: { url: string };
    small: { url: string };
    medium: { url: string };
    large: { url: string };
  };
}

interface Banner {
  id: string;
  background_image: BannerImage;
  cover_image: BannerImage;
  product_offering_headline: string;
  offer_description: string;
  button_text: string;
  redirect_url?: string;
  post_status: string;
  discount_percentage?: string | number;
  subtitle?: string;
}

const BannerAdvanced: React.FC = () => {
  const { data, isLoading, isError } = useGetBannersQuery({
    page: 1,
    limit: 100000,
  });
  console.log("🚀 ~ isError:", isError);
  console.log("🚀 ~ data:", data);

  const bannerData = data?.data?.data as Banner[] | undefined;

  // Use our custom hook with banner data as dependency
  const { sliderRef } = useGlideSlider({}, [bannerData]);

  // Function to handle banner button clicks
  const handleBannerClick = () => {
    // Scroll to top when a banner is clicked
    scrollToTop();
  };

  if (isLoading) {
    return (
      <div className="relative overflow-hidden">
        <div className="glide__track">
          <ul className="glide__slides">
            {[1].map((index) => (
              <li key={index} className="glide__slide">
                <BannerItemSkeleton />
              </li>
            ))}
          </ul>
        </div>
      </div>
    );
  }

  return (
    <div>
      {!bannerData || bannerData.length == 0 ? (
        <div className="min-h-[100px] bg-[#F7F9FA]"></div>
      ) : (
        <div className="relative overflow-hidden" ref={sliderRef}>
          <div className="glide__track" data-glide-el="track">
            <ul className="glide__slides">
              {bannerData?.map((banner: Banner, index: number) => (
                <li key={index} className="glide__slide">
                  <BannerItem
                    banner={{
                      backgroundImage: banner?.background_image?.url,
                      coverImage: banner?.cover_image?.url,
                      heading: banner?.product_offering_headline,
                      description: banner?.offer_description,
                      buttonText: banner?.button_text,
                      link: banner?.redirect_url || "/",
                      discount_percentage:
                        banner?.discount_percentage?.toString(),
                      subtitle: banner?.subtitle,
                    }}
                    onButtonClick={handleBannerClick}
                  />
                </li>
              ))}
            </ul>
          </div>
          <div className="glide__arrows" data-glide-el="controls">
            <button
              className="absolute left-1 top-1/2 z-10 flex aspect-square w-12 -translate-y-1/2 items-center justify-center rounded-full border-2 border-[#FFA600] text-white min-[1460px]:left-5"
              data-glide-dir="<"
            >
              <FaChevronLeft />
            </button>
            <button
              className="absolute right-1 top-1/2 z-10 flex aspect-square w-12 -translate-y-1/2 items-center justify-center rounded-full border-2 border-[#FFA600] text-white min-[1460px]:right-5"
              data-glide-dir=">"
            >
              <FaChevronRight />
            </button>
          </div>
          <div className="glide__bullets" data-glide-el="controls[nav]">
            {bannerData?.map((_: Banner, index: number) => (
              <button
                key={index}
                className="glide__bullet"
                data-glide-dir={`=${index}`}
                aria-label={`Go to slide ${index + 1}`}
              ></button>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default BannerAdvanced;
