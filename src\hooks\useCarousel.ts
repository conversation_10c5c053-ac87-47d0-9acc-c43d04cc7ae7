import { useState, useEffect, useRef } from "react";
import type Glide from "@glidejs/glide";
import type { CarouselApi } from "@/components/ui/carousel";

// Type for Glide options
export interface GlideOptions {
  type?: "carousel" | "slider";
  startAt?: number;
  perView?: number;
  gap?: number;
  autoplay?: number | false;
  hoverpause?: boolean;
  rewind?: boolean;
  bound?: boolean;
  focusAt?: number | string;
  keyboard?: boolean;
  swipeThreshold?: number | boolean;
  dragThreshold?: number | boolean;
  perTouch?: number | boolean;
  touchRatio?: number;
  touchAngle?: number;
  animationDuration?: number;
  rewindDuration?: number;
  animationTimingFunc?: string;
  direction?: "ltr" | "rtl";
  peek?: number | { before: number; after: number };
  breakpoints?: Record<string, Partial<GlideOptions>>;
  classes?: {
    swipeable: string;
    dragging: string;
    direction: Record<"ltr" | "rtl", string>;
    type: Record<"carousel" | "slider", string>;
    slide: Record<"clone" | "active", string>;
    arrow: Record<"disabled", string>;
    nav: Record<"active", string>;
  };
}

/**
 * Custom hook for managing built-in Carousel component
 * @param autoRotateInterval - Milliseconds between auto-rotation (default: 5000ms)
 * @returns API for controlling carousel
 */
export const useCarouselControl = (autoRotateInterval = 5000) => {
  const [api, setApi] = useState<CarouselApi>();
  const [activeIndex, setActiveIndex] = useState(0);

  useEffect(() => {
    if (!api) return;

    // Handle carousel selection changes
    const handleSelect = () => {
      setActiveIndex(api.selectedScrollSnap());
    };

    // Set up event listeners
    api.on("select", handleSelect);
    setActiveIndex(api.selectedScrollSnap());

    // Set up auto-rotation if interval is positive
    let interval: NodeJS.Timeout | null = null;

    if (autoRotateInterval > 0) {
      interval = setInterval(() => {
        api.scrollNext();
      }, autoRotateInterval);
    }

    // Clean up on unmount
    return () => {
      api.off("select", handleSelect);
      if (interval) clearInterval(interval);
    };
  }, [api, autoRotateInterval]);

  // Navigation functions
  const handleItemClick = (index: number) => {
    setActiveIndex(index);
    if (api) {
      api.scrollTo(index);
    }
  };

  const next = () => api?.scrollNext();
  const prev = () => api?.scrollPrev();

  return {
    api,
    setApi,
    activeIndex,
    handleItemClick,
    next,
    prev,
  };
};

/**
 * Custom hook for managing Glide.js carousel instances
 * @param options - Configuration options for Glide.js
 * @param dependencies - Array of dependencies that should trigger re-initialization
 * @returns Reference to attach to the slider element
 */
export const useGlideSlider = (
  options: GlideOptions = {},
  dependencies: any[] = [],
) => {
  const sliderRef = useRef<HTMLDivElement | null>(null);
  const glideInstanceRef = useRef<Glide | null>(null);

  useEffect(() => {
    const importGlide = async () => {
      // Only initialize if we have a valid slider element and all dependencies are resolved
      if (
        sliderRef.current &&
        dependencies.every((dep) => dep !== undefined && dep !== null)
      ) {
        try {
          // Dynamic import of Glide
          const GlideModule = await import("@glidejs/glide");
          const GlideConstructor = GlideModule.default;

          // Destroy existing instance if there is one
          if (glideInstanceRef.current) {
            glideInstanceRef.current.destroy();
          }

          // Default options
          const defaultOptions: GlideOptions = {
            type: "carousel",
            startAt: 0,
            perView: 1,
            gap: 20,
            autoplay: 5000,
            hoverpause: true,
          };

          // Create and mount new instance
          const glide = new GlideConstructor(sliderRef.current, {
            ...defaultOptions,
            ...options,
          });

          glide.mount();
          glideInstanceRef.current = glide;
        } catch (error) {
          console.error("Error initializing Glide slider:", error);
        }
      }
    };

    importGlide();

    // Cleanup on unmount
    return () => {
      if (glideInstanceRef.current) {
        glideInstanceRef.current.destroy();
        glideInstanceRef.current = null;
      }
    };
  }, dependencies);

  return { sliderRef };
};
