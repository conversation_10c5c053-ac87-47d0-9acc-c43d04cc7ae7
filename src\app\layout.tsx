import "swiper/css";
import "swiper/css/navigation";
import type { Metada<PERSON>, Viewport } from "next";
import { <PERSON><PERSON><PERSON> } from "next/font/google";
import "./globals.css";
import { StoreProvider } from "@/store/StoreProvider";
import "@glidejs/glide/dist/css/glide.core.min.css";
import "@glidejs/glide/dist/css/glide.theme.min.css";
import "react-toastify/dist/ReactToastify.css";
// import { ToastContainer, <PERSON>un<PERSON> } from "react-toastify";
import WarrantyDialog from "@/components/common/dialogs/WarrantyDialog";
import NProgressBar from "@/components/common/NProgress";
import { <PERSON><PERSON><PERSON>, ToastContainer } from "react-toastify";
import Script from "next/script";

const poppins = Poppins({
  weight: ["100", "200", "300", "400", "500", "600", "700", "800", "900"],
  subsets: ["latin"],
  style: ["italic", "normal"],
  display: "swap",
  preload: true,
});

export const metadata: Metadata = {
  title: "MetBlinds",
  description: "metblinds",
  icons: {
    icon: "/logo.webp",
  },
  manifest: "/manifest.json",
  applicationName: "MetBlinds",
  authors: [{ name: "MetBlinds" }],
  generator: "Next.js",
  keywords: ["blinds", "window treatments", "home decor"],
  robots: {
    index: true,
    follow: true,
  },
};

export const viewport: Viewport = {
  width: "device-width",
  initialScale: 1,
  maximumScale: 1,
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <head>
        <link rel="preconnect" href="https://fonts.googleapis.com" />
        <link
          rel="preconnect"
          href="https://fonts.gstatic.com"
          crossOrigin="anonymous"
        />
        <Script
          async
          src="https://www.googletagmanager.com/gtag/js?id=AW-16719712444"
        />
        <Script
          id="gtag-init"
          strategy="afterInteractive"
          dangerouslySetInnerHTML={{
            __html: `
              window.dataLayer = window.dataLayer || [];
              function gtag(){dataLayer.push(arguments);}
              gtag('js', new Date());
              gtag('config', 'AW-16719712444');
            `,
          }}
        />
      </head>
      <body className={`${poppins.className} antialiased`}>
        <StoreProvider>
          <NProgressBar />
          {children}
          <WarrantyDialog />
        </StoreProvider>
        <ToastContainer
          position="top-right"
          autoClose={4000}
          hideProgressBar={false}
          newestOnTop={false}
          closeOnClick={false}
          rtl={false}
          pauseOnFocusLoss
          draggable
          pauseOnHover
          theme="light"
          transition={Bounce}
        />
      </body>
    </html>
  );
}
