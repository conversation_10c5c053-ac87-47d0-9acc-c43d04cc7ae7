"use client";

import { useEffect, useRef } from "react";
import Glide from "@glidejs/glide";
import { FaChevronLeft, FaChevronRight } from "react-icons/fa";
import Link from "next/link";
import Image from "next/image";
import "./ContactBanner.css";

type BannerData = {
  id: string;
  backgroundImage: string;
  heading: string;
  description: string;
  buttonText?: string;
  buttonLink?: string;
};

type Props = { data: BannerData[] };

const ContactBanner = ({ data }: Props) => {
  const sliderRef = useRef<HTMLDivElement>(null);

 useEffect(() => {
   if (sliderRef.current) {
     const glide = new Glide(sliderRef.current, {
       type: "carousel",
       startAt: 0,
       perView: 1,
       gap: 0,
       autoplay: 5000,
       hoverpause: true,
     });

     glide.mount();

     return () => {
       glide.destroy();
     };
   }
 }, []);

  return (
    <div className="relative overflow-hidden" ref={sliderRef}>
      <div className="glide__track" data-glide-el="track">
        <ul className="glide__slides">
          {data.map((banner) => (
            <li key={banner.id} className="glide__slide">
              {/* ✅ Background Image with Overlay */}
              <div className="relative h-[700px] w-full overflow-hidden md:h-[927px]">
                <Image
                  src={banner.backgroundImage}
                  alt="Banner background"
                  fill
                  quality={85}
                  className="z-[-2] object-cover object-center"
                  priority
                  sizes="100vw"
                />
                <div className="absolute inset-0 z-[-1] bg-black/10" />
                <div className="absolute inset-0 z-[-1] bg-gradient-to-t from-black/90 to-transparent opacity-35" />

                {/* Content */}
                <div className="h-full px-4">
                  <div className="contact-banner-text-container mx-auto flex h-full max-w-[1450px] flex-col items-center justify-center">
                    <div className="flex w-full max-w-[800px] flex-col items-center gap-5 px-10 min-[900px]:px-2">
                      <h1 className="text-center text-4xl font-semibold text-white md:text-5xl lg:text-6xl">
                        {banner.heading}
                      </h1>

                      {/* ✅ Decorative Icon */}
                      <div className="relative h-2 w-20">
                        <Image
                          src="/svgs/divider-icon.svg"
                          alt="divider"
                          fill
                          className="object-contain"
                          sizes="80px"
                        />
                      </div>

                      <p className="text-center text-base text-white md:text-lg">
                        {banner.description}
                      </p>

                      {banner.buttonText && banner.buttonLink && (
                        <Link
                          href={banner.buttonLink}
                          className="rounded-full bg-[#FFA600] px-5 py-2.5 text-white transition-colors duration-300 hover:bg-[#FFB733] sm:px-8 sm:py-4"
                        >
                          {banner.buttonText}
                        </Link>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            </li>
          ))}
        </ul>
      </div>

      {/* Navigation Arrows */}
      <div className="glide__arrows" data-glide-el="controls">
        <button
          className="absolute left-1 top-1/2 z-10 flex aspect-square w-12 -translate-y-1/2 items-center justify-center rounded-full border-2 border-[#FFA600] text-white min-[900px]:left-5"
          data-glide-dir="<"
          aria-label="Previous slide"
        >
          <FaChevronLeft />
        </button>
        <button
          className="absolute right-1 top-1/2 z-10 flex aspect-square w-12 -translate-y-1/2 items-center justify-center rounded-full border-2 border-[#FFA600] text-white min-[900px]:right-5"
          data-glide-dir=">"
          aria-label="Next slide"
        >
          <FaChevronRight />
        </button>
      </div>
    </div>
  );
};

export default ContactBanner;
