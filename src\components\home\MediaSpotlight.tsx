"use client";

import { homePage } from "@/configs/pages-data/home";
import useScrollToSection from "@/hooks/useScrollToSection";
import React from "react";
import Image from "next/image";

const formatTextWithBold = (text: string) => {
  return text.split(/(\*[^*]+\*)/).map((part, index) => {
    if (part.startsWith("*") && part.endsWith("*")) {
      return <strong key={index}>{part.slice(1, -1)}</strong>;
    }
    return part;
  });
};

const MediaSpotlight: React.FC<{ data: typeof homePage.mediaSpotlight }> = ({
  data,
}) => {
  const scrollTo = useScrollToSection();

  return (
    <div className="relative overflow-hidden py-16 xl:py-32">
      {/* ✅ Background Image */}
      <Image
        src={data.background}
        alt="Media spotlight background"
        fill
        priority
        quality={100}
        style={{
          objectFit: "fill",
          objectPosition: " center",
          maxWidth: "1600px",
          margin: "0 auto",
        }}
      />

      {/* ✅ Gradient overlay */}
      <div className="absolute inset-0 z-[10] bg-gradient-to-b from-white via-white/70 to-white/0" />

      {/* ✅ Content */}
      <div className="relative z-10 mx-auto max-w-[1400px] px-3 md:px-5">
        <div className="flex flex-col items-center justify-center gap-4 sm:gap-7 md:gap-10">
          <h2 className="max-w-[850px] text-center text-2xl text-[#013F68] sm:text-3xl md:text-5xl">
            {formatTextWithBold(data.heading)}
          </h2>
          <h4 className="max-w-[670px] text-center text-lg text-[#474747] sm:text-xl">
            {data.subHeading}
          </h4>
          <button
            className="rounded-full bg-[#FFA600] px-4 py-2.5 text-lg text-white sm:px-8 sm:text-xl md:text-2xl"
            onClick={() => scrollTo("contact")}
          >
            <span>Give us a call</span>
          </button>
        </div>
      </div>
    </div>
  );
};

export default MediaSpotlight;
