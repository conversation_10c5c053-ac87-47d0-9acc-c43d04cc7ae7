"use client";
import { rubik } from "@/fonts";
import FAQsCard from "./cards/FAQsCard";

interface FAQItem {
  question: string;
  answer: string;
}

type Props = {
  data: {
    heading: string;
    faqs: FAQItem[];
  };
};
const FAQsSection = ({ data }: Props) => {
  return (
    <div className="pb-16 sm:pb-20 md:pb-24 lg:pb-28">
      <div className="mx-auto w-full max-w-[1420px] px-4 sm:px-6 md:px-8">
        <div className="flex justify-center">
          <h2
            className={`${rubik.className} mb-8 rounded-full bg-[#FFBB3D] px-6 py-2 text-center text-2xl font-semibold text-[#013F68] sm:mb-10 sm:px-8 sm:py-2.5 sm:text-3xl md:mb-12 md:px-10 md:text-[32px] lg:mb-14 lg:px-12 lg:text-[40px]`}
          >
            {data.heading}
          </h2>
        </div>

        <div className="grid grid-cols-1 gap-3 sm:gap-4 md:gap-5">
          {data.faqs.map((faq, index) => (
            <FAQsCard key={index} question={faq.question} answer={faq.answer} />
          ))}
        </div>
      </div>
    </div>
  );
};

export default FAQsSection;
