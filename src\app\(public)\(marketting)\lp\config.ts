export const pageData = {
  // Header Banner
  headerBanner: {
    discountText: "40%",
    offerText: "OFF CUSTOM BLINDS AND FREE INSTALLATION",
  },

  // Header Navbar
  navbar: {
    logo: {
      width: 200,
      height: 50,
    },
    contactButton: {
      text: "Contact US",
      href: "/contact",
    },
  },

  // Hero Section
  hero: {
    backgroundImage: "/backgrounds/lp.webp",
    specialOffer: {
      badge: "SPECIAL OFFER",
      title: "Upto 50% OFF Custom Blinds",
      titleColor: "#FFA600",
    },
    benefits: [
      "FREE Window Blinds Installation",
      "Discounted Motorization Upgrade",
    ],
    pricing: {
      originalPrice: "$250",
      discountedPrice: "$200",
      discountColor: "#FFA600",
    },
    description:
      "Mention this offer when you request an estimate (phone, form, and email) & Save!",
    ctaButton: {
      text: "Request A Free Estimate",
      bgColor: "#FFA600",
      textColor: "#013F68",
    },
    certificates: [
      {
        src: "/certificate1.png",
        alt: "Certificate1",
        width: 224,
      },
      {
        src: "/certificate2.png",
        alt: "Certificate2",
        width: 140,
      },
    ],
    formSection: {
      award: {
        src: "/award.png",
        alt: "Award",
        width: 108,
        height: 124,
      },
      title: "Request A Free Estimate",
      contactButton: {
        text: "Contact US",
        bgColor: "#FFA600",
        textColor: "#013F68",
      },
    },
  },

  // Colors
  colors: {
    primary: "#013F68", // Dark blue
    accent: "#FFA600", // Orange
    white: "#FFFFFF",
    black: "#000000",
  },

  // Typography
  typography: {
    fontSizes: {
      badge: "text-2xl",
      heroTitle: "text-7xl",
      description: "text-xl",
      pricing: "text-3xl",
      benefitsList: "text-lg",
      formTitle: "text-4xl",
    },
    fontWeights: {
      medium: "font-medium",
      semibold: "font-semibold",
      bold: "font-bold",
    },
  },
};
